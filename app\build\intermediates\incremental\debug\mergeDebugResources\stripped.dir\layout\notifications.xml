<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <!-- Today Section -->

    <RelativeLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@android:color/white"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="-16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="92dp"
            android:layout_height="80dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:tint="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseAppTint" />

        <TextView
            android:layout_width="215dp"
            android:layout_height="wrap_content"
            android:layout_alignEnd="@+id/button"
            android:layout_alignParentStart="true"
            android:layout_marginStart="119dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="94dp"
            android:text="@string/notifications"
            android:textColor="#101518"
            android:textSize="25sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@android:color/transparent"
            android:textColor="#101518"
            android:textSize="16sp"
            android:textStyle="bold">


        </Button>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="394dp"
        android:layout_height="553dp"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toTopOf="@id/clear_all_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <!-- Notification Item 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#f0f3f5"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/warning_icon"
                android:src="@drawable/warning_24px" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/budget_alert"
                    android:textColor="#101518"
                    android:textSize="16sp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/you_ve_spent_70_of_your_budget_for_the_month"
                    android:textColor="#5e788d"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- Notification Item 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#f0f3f5"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/currency_icon"
                android:src="@drawable/attach_money" />

            <LinearLayout
                android:layout_width="309dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction"
                    android:textColor="#101518"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/you_ve_received_25_from_sarah"
                    android:textColor="#5e788d"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- More notifications go here -->

    </LinearLayout>
    <!-- Clear All Button -->


    <Button
        android:id="@+id/clear_all_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/clear_all"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@color/primary_blue"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:layout_marginBottom="16dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        app:layout_constraintBottom_toBottomOf="parent"
    />

</androidx.constraintlayout.widget.ConstraintLayout>
