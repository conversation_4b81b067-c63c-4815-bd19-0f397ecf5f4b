// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class AddtransactionBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageButton backButton;

  @NonNull
  public final TextView budgetInfo;

  @NonNull
  public final EditText editText4;

  @NonNull
  public final EditText editText5;

  @NonNull
  public final TextView itemAmountHeader;

  @NonNull
  public final TextView itemDateHeader;

  @NonNull
  public final TextView itemNameHeader;

  @NonNull
  public final LinearLayout linearLayout;

  @NonNull
  public final LinearLayout linearLayout3;

  @NonNull
  public final LinearLayout linearLayout5;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final TextView title;

  private AddtransactionBinding(@NonNull RelativeLayout rootView, @NonNull ImageButton backButton,
      @NonNull TextView budgetInfo, @NonNull EditText editText4, @NonNull EditText editText5,
      @NonNull TextView itemAmountHeader, @NonNull TextView itemDateHeader,
      @NonNull TextView itemNameHeader, @NonNull LinearLayout linearLayout,
      @NonNull LinearLayout linearLayout3, @NonNull LinearLayout linearLayout5,
      @NonNull RecyclerView recyclerView, @NonNull RelativeLayout rootLayout,
      @NonNull Button saveButton, @NonNull RelativeLayout spinnerContainer,
      @NonNull TextView title) {
    this.rootView = rootView;
    this.backButton = backButton;
    this.budgetInfo = budgetInfo;
    this.editText4 = editText4;
    this.editText5 = editText5;
    this.itemAmountHeader = itemAmountHeader;
    this.itemDateHeader = itemDateHeader;
    this.itemNameHeader = itemNameHeader;
    this.linearLayout = linearLayout;
    this.linearLayout3 = linearLayout3;
    this.linearLayout5 = linearLayout5;
    this.recyclerView = recyclerView;
    this.rootLayout = rootLayout;
    this.saveButton = saveButton;
    this.spinnerContainer = spinnerContainer;
    this.title = title;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static AddtransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static AddtransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.addtransaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static AddtransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.back_button;
      ImageButton backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.budget_info;
      TextView budgetInfo = ViewBindings.findChildViewById(rootView, id);
      if (budgetInfo == null) {
        break missingId;
      }

      id = R.id.editText4;
      EditText editText4 = ViewBindings.findChildViewById(rootView, id);
      if (editText4 == null) {
        break missingId;
      }

      id = R.id.editText5;
      EditText editText5 = ViewBindings.findChildViewById(rootView, id);
      if (editText5 == null) {
        break missingId;
      }

      id = R.id.itemAmountHeader;
      TextView itemAmountHeader = ViewBindings.findChildViewById(rootView, id);
      if (itemAmountHeader == null) {
        break missingId;
      }

      id = R.id.itemDateHeader;
      TextView itemDateHeader = ViewBindings.findChildViewById(rootView, id);
      if (itemDateHeader == null) {
        break missingId;
      }

      id = R.id.itemNameHeader;
      TextView itemNameHeader = ViewBindings.findChildViewById(rootView, id);
      if (itemNameHeader == null) {
        break missingId;
      }

      id = R.id.linearLayout;
      LinearLayout linearLayout = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout == null) {
        break missingId;
      }

      id = R.id.linearLayout3;
      LinearLayout linearLayout3 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout3 == null) {
        break missingId;
      }

      id = R.id.linearLayout5;
      LinearLayout linearLayout5 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout5 == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.save_button;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new AddtransactionBinding((RelativeLayout) rootView, backButton, budgetInfo, editText4,
          editText5, itemAmountHeader, itemDateHeader, itemNameHeader, linearLayout, linearLayout3,
          linearLayout5, recyclerView, rootLayout, saveButton, spinnerContainer, title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
