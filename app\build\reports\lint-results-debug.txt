C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:120: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                        ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:120: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:136: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:136: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                          ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:311: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:311: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:330: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:330: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt:338: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
    private fun getExpenses(@NonNull response: Response<List<Record>>): List<Expenses>? {
                            ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:128: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                    override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                            ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:128: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
                    override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:142: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                    override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:142: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                    override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:204: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:204: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:219: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                        ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:219: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:227: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:227: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                                                  ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:244: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:244: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt:12: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewHolder not ending with ? [KotlinNullnessAnnotation]
    @NonNull
    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt:13: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewGroup not ending with ? [KotlinNullnessAnnotation]
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt:19: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewHolder not ending with ? [KotlinNullnessAnnotation]
    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {
                                  ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt:12: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewHolder not ending with ? [KotlinNullnessAnnotation]
    @NonNull
    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt:13: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewGroup not ending with ? [KotlinNullnessAnnotation]
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt:19: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type ViewHolder not ending with ? [KotlinNullnessAnnotation]
    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {
                                  ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:168: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:168: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:186: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:186: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:217: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:217: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:225: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:225: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:234: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type CharSequence not ending with ? [KotlinNullnessAnnotation]
            override fun onAuthenticationError(errorCode: Int, @NonNull errString: CharSequence) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt:239: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type BiometricPrompt.AuthenticationResult not ending with ? [KotlinNullnessAnnotation]
            override fun onAuthenticationSucceeded(@NonNull result: BiometricPrompt.AuthenticationResult) {
                                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt:84: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt:84: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt:116: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt:116: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt:177: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt:177: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt:186: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt:186: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:167: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                    override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                            ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:167: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                    override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:195: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
                    override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:195: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                    override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:209: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type KeyEvent not ending with ? [KotlinNullnessAnnotation]
    override fun onKeyDown(keyCode: Int, @NonNull event: KeyEvent): Boolean {
                                         ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:271: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:271: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:299: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:299: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:353: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:353: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:365: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:365: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:382: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:382: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:392: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:392: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:17: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Context not ending with ? [KotlinNullnessAnnotation]
    @NonNull context: Context,
    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:18: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type WorkerParameters not ending with ? [KotlinNullnessAnnotation]
    @NonNull params: WorkerParameters
    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:24: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Result not ending with ? [KotlinNullnessAnnotation]
    @NonNull
    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:98: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:98: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:106: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt:106: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:141: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:141: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:162: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                        override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:162: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
                        override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                                           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:176: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                        override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:176: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                        override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                                          ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:188: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:188: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:214: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:214: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:228: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                        ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:228: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                                                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:236: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<Void> not ending with ? [KotlinNullnessAnnotation]
                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:236: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                                                                  ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:248: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:248: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:287: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:287: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:307: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:307: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:322: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                    ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:322: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Response<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                                                                       ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:339: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Call<List<Record>> not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                   ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:339: Warning: Do not use @NonNull in Kotlin; the nullability is already implied by the Kotlin type Throwable not ending with ? [KotlinNullnessAnnotation]
            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                                                                      ~~~~~~~~

   Explanation for issues of type "KotlinNullnessAnnotation":
   In Kotlin, nullness is part of the type system; s: String is never null and
   s: String? is sometimes null, whether or not you add in additional
   annotations stating @NonNull or @Nullable. These are likely copy/paste
   mistakes, and are misleading.

C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:14: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk = 34
        ~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\LanguageActivity.kt:41: Warning: Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the bundle configuration [AppBundleLocaleChanges]
        config.setLocale(locale)
        ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AppBundleLocaleChanges":
   When changing locales at runtime (e.g. to provide an in-app language
   switcher), the Android App Bundle must be configured to not split by locale
   or the Play Core library must be used to download additional locales at
   runtime.

   https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes

C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2) [AndroidGradlePluginVersion]
agp = "8.5.1"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:64: Warning: BOM should be added with a call to platform() [BomWithoutPlatform]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BomWithoutPlatform":
   When including a BOM, the dependency's coordinates must be wrapped in a
   call to platform() for Gradle to interpret it correctly.

   https://developer.android.com/r/tools/gradle-bom-docs

C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:64: Warning: A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0 [GradleDependency]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:65: Warning: A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1 [GradleDependency]
    implementation ("androidx.work:work-runtime:2.8.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:12: Warning: A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.6.0"
                     ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:13: Warning: A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0 [GradleDependency]
navigationUi = "2.6.0"
               ~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:14: Warning: A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0 [GradleDependency]
firebaseBom = "32.1.2"
              ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml:16: Warning: A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1 [GradleDependency]
firebaseAuth = "23.0.0"
               ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:173: Warning: This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.ExpensesActivity. You could either make the component com.example.budgettracker.ExpensesActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer. [UnsafeIntentLaunch]
            val intent = getIntent()
                         ~~~~~~~~~~~
    C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt:175: The unsafe intent is launched here.
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:123: Warning: This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.TransactionsActivity. You could either make the component com.example.budgettracker.TransactionsActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer. [UnsafeIntentLaunch]
            val intent = getIntent()
                         ~~~~~~~~~~~
    C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:125: The unsafe intent is launched here.

   Explanation for issues of type "UnsafeIntentLaunch":
   Intent that potentially could come from an untrusted source should not be
   launched from an unprotected component without first being sanitized. See
   this support FAQ for details:
   https://support.google.com/faqs/answer/9267555

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:64: Warning: Invalid layout param in a LinearLayout: layout_below [ObsoleteLayoutParam]
               android:layout_below="@id/description"
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:87: Warning: Invalid layout param in a LinearLayout: layout_below [ObsoleteLayoutParam]
               android:layout_below="@id/description"
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteLayoutParam":
   The given layout_param is not defined for the given layout, meaning it has
   no effect. This usually happens when you change the parent layout or move
   view code around without updating the layout params. This will cause
   useless attribute processing at runtime, and is misleading for others
   reading the layout so the parameter should be removed.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\eye_close.xml:9: Warning: Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M644,532L586,474Q595,427 559,386Q523,345 466,354L408,296Q425,288 442.5,284Q460,280 480,280Q555,280 607.5,332.5Q660,385 660,460Q660,480 656,497.5Q652,515 644,532ZM772,658L714,602Q752,573 781.5,538.5Q811,504 832,460Q782,359 688.5,299.5Q595,240 480,240Q451,240 423,244Q395,248 368,256L306,194Q347,177 390,168.5Q433,160 480,160Q631,160 749,243.5Q867,327 920,460Q897,519 859.5,569.5Q822,620 772,658ZM792,904L624,738Q589,749 553.5,754.5Q518,760 480,760Q329,760 211,676.5Q93,593 40,460Q61,407 93,361.5Q125,316 166,280L56,168L112,112L848,848L792,904ZM222,336Q193,362 169,393Q145,424 128,460Q178,561 271.5,620.5Q365,680 480,680Q500,680 519,677.5Q538,675 558,672L522,634Q511,637 501,638.5Q491,640 480,640Q405,640 352.5,587.5Q300,535 300,460Q300,449 301.5,439Q303,429 306,418L222,336ZM541,429L541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429ZM390,504Q390,504 390,504Q390,504 390,504L390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\fingerprint.xml:9: Warning: Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M481,179Q587,179 681,224.5Q775,270 838,356Q845,365 842.5,372Q840,379 834,384Q828,389 820,388.5Q812,388 806,380Q751,302 664.5,260.5Q578,219 481,219Q384,219 299,260.5Q214,302 158,380Q152,389 144,390Q136,391 130,386Q123,381 121.5,373.5Q120,366 126,358Q188,273 281.5,226Q375,179 481,179ZM481,273Q616,273 713,363Q810,453 810,586Q810,636 774.5,669.5Q739,703 688,703Q637,703 600.5,669.5Q564,636 564,586Q564,553 539.5,530.5Q515,508 481,508Q447,508 422.5,530.5Q398,553 398,586Q398,683 455.5,748Q513,813 604,839Q613,842 616,849Q619,856 617,864Q615,871 609,876Q603,881 594,879Q490,853 424,775.5Q358,698 358,586Q358,536 394,502Q430,468 481,468Q532,468 568,502Q604,536 604,586Q604,619 629,641.5Q654,664 688,664Q722,664 746,641.5Q770,619 770,586Q770,470 685,391Q600,312 482,312Q364,312 279,391Q194,470 194,585Q194,609 198.5,645Q203,681 220,729Q223,738 219.5,745Q216,752 208,755Q200,758 192.5,754.5Q185,751 182,743Q167,704 160.5,665.5Q154,627 154,586Q154,453 250.5,363Q347,273 481,273ZM481,81Q545,81 606,96.5Q667,112 724,141Q733,146 734.5,153Q736,160 733,167Q730,174 723,178Q716,182 706,177Q653,150 596.5,135.5Q540,121 481,121Q423,121 367,134.5Q311,148 260,177Q252,182 244,179.5Q236,177 232,169Q228,161 230,154.5Q232,148 240,143Q296,113 357,97Q418,81 481,81ZM481,370Q574,370 641,432.5Q708,495 708,586Q708,595 702.5,600.5Q697,606 688,606Q680,606 674,600.5Q668,595 668,586Q668,511 612.5,460.5Q557,410 481,410Q405,410 350.5,460.5Q296,511 296,586Q296,667 324,723.5Q352,780 406,837Q412,843 412,851Q412,859 406,865Q400,871 392,871Q384,871 378,865Q319,803 287.5,738.5Q256,674 256,586Q256,495 322,432.5Q388,370 481,370ZM480,566Q489,566 494.5,572Q500,578 500,586Q500,661 554,709Q608,757 680,757Q686,757 697,756Q708,755 720,753Q729,751 735.5,755.5Q742,760 744,769Q746,777 741,783Q736,789 728,791Q710,796 696.5,796.5Q683,797 680,797Q591,797 525.5,737Q460,677 460,586Q460,578 465.5,572Q471,566 480,566Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\person.xml:9: Warning: Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M480,480Q414,480 367,433Q320,386 320,320Q320,254 367,207Q414,160 480,160Q546,160 593,207Q640,254 640,320Q640,386 593,433Q546,480 480,480ZM160,800L160,688Q160,654 177.5,625.5Q195,597 224,582Q286,551 350,535.5Q414,520 480,520Q546,520 610,535.5Q674,551 736,582Q765,597 782.5,625.5Q800,654 800,688L800,800L160,800ZM240,720L720,720L720,688Q720,677 714.5,668Q709,659 700,654Q646,627 591,613.5Q536,600 480,600Q424,600 369,613.5Q314,627 260,654Q251,659 245.5,668Q240,677 240,688L240,720ZM480,400Q513,400 536.5,376.5Q560,353 560,320Q560,287 536.5,263.5Q513,240 480,240Q447,240 423.5,263.5Q400,287 400,320Q400,353 423.5,376.5Q447,400 480,400ZM480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320ZM480,720L480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720L480,720Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\settings_24px.xml:9: Warning: Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M370,880L354,752Q341,747 329.5,740Q318,733 307,725L188,775L78,585L181,507Q180,500 180,493.5Q180,487 180,480Q180,473 180,466.5Q180,460 181,453L78,375L188,185L307,235Q318,227 330,220Q342,213 354,208L370,80L590,80L606,208Q619,213 630.5,220Q642,227 653,235L772,185L882,375L779,453Q780,460 780,466.5Q780,473 780,480Q780,487 780,493.5Q780,500 778,507L881,585L771,775L653,725Q642,733 630,740Q618,747 606,752L590,880L370,880ZM440,800L519,800L533,694Q564,686 590.5,670.5Q617,655 639,633L738,674L777,606L691,541Q696,527 698,511.5Q700,496 700,480Q700,464 698,448.5Q696,433 691,419L777,354L738,286L639,328Q617,305 590.5,289.5Q564,274 533,266L520,160L441,160L427,266Q396,274 369.5,289.5Q343,305 321,327L222,286L183,354L269,418Q264,433 262,448Q260,463 260,480Q260,496 262,511Q264,526 269,541L183,606L222,674L321,632Q343,655 369.5,670.5Q396,686 427,694L440,800ZM482,620Q540,620 581,579Q622,538 622,480Q622,422 581,381Q540,340 482,340Q423,340 382.5,381Q342,422 342,480Q342,538 382.5,579Q423,620 482,620ZM480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480Z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml:237: Warning: Use a layout_width of 0dp instead of 185dp for better performance [InefficientWeight]
        android:layout_width="185dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InefficientWeight":
   When only a single widget in a LinearLayout defines a weight, it is more
   efficient to assign a width/height of 0dp to it since it will absorb all
   the remaining space anyway. With a declared width/height of 0dp it does not
   have to measure its own size first.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml:8: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\base.xml:7: Warning: Possible overdraw: Root element paints background @color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\dashboard.xml:8: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\language.xml:5: Warning: Possible overdraw: Root element paints background @color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\login.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base_Theme_BudgetTracker) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\notifications.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
       android:background="@android:color/white"
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml:16: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Base.Theme.BudgetTracker) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\activity_main.xml:2: Warning: The resource R.layout.activity_main appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\button_ripple.xml:2: Warning: The resource R.drawable.button_ripple appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.onPrimary appears to be unused [UnusedResources]
    <color name="onPrimary">#ffffff</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.border_color appears to be unused [UnusedResources]
    <color name="border_color">#dae1e7</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.back_ground appears to be unused [UnusedResources]
    <color name="back_ground">#b0463a</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\content_main.xml:2: Warning: The resource R.layout.content_main appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\dimens.xml:2: Warning: The resource R.dimen.fab_margin appears to be unused [UnusedResources]
    <dimen name="fab_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher.webp: Warning: The resource R.mipmap.ic_launcher appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp: Warning: The resource R.mipmap.ic_launcher_round appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\mail_24px.xml:1: Warning: The resource R.drawable.mail_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\menu\menu_main.xml:1: Warning: The resource R.menu.menu_main appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\monitoring.xml:1: Warning: The resource R.drawable.monitoring appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\navigation\nav_graph.xml:2: Warning: The resource R.navigation.nav_graph appears to be unused [UnusedResources]
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\search.xml:1: Warning: The resource R.drawable.search appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\settings_24px.xml:1: Warning: The resource R.drawable.settings_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_background.xml:17: Warning: The resource R.drawable.spinner_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\spinner_item.xml:2: Warning: The resource R.layout.spinner_item appears to be unused [UnusedResources]
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_normal.xml:2: Warning: The resource R.drawable.spinner_normal appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_press.xml:2: Warning: The resource R.drawable.spinner_press appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_select.xml:2: Warning: The resource R.drawable.spinner_select appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.action_settings appears to be unused [UnusedResources]
    <string name="action_settings">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.first_fragment_label appears to be unused [UnusedResources]
    <string name="first_fragment_label">First Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.second_fragment_label appears to be unused [UnusedResources]
    <string name="second_fragment_label">Second Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.next appears to be unused [UnusedResources]
    <string name="next">Next</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.previous appears to be unused [UnusedResources]
    <string name="previous">Previous</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.search_description appears to be unused [UnusedResources]
    <string name="search_description">Search</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.chart_description appears to be unused [UnusedResources]
    <string name="chart_description">Chart</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.cashmate appears to be unused [UnusedResources]
    <string name="cashmate">CashMate</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.today appears to be unused [UnusedResources]
    <string name="today">Today</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.select_category appears to be unused [UnusedResources]
    <string name="select_category">Select category</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:31: Warning: The resource R.string._5_943 appears to be unused [UnusedResources]
    <string name="_5_943">$5,943</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:32: Warning: The resource R.string.last_30_days appears to be unused [UnusedResources]
    <string name="last_30_days">This month</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.meta appears to be unused [UnusedResources]
    <string name="meta">META</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.meta_platforms appears to be unused [UnusedResources]
    <string name="meta_platforms">Meta Platforms</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.budget_info appears to be unused [UnusedResources]
    <string name="budget_info">Your budget for this month is $%d</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:61: Warning: The resource R.string.user_profile appears to be unused [UnusedResources]
    <string name="user_profile">user profile</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:73: Warning: The resource R.string.main_cash appears to be unused [UnusedResources]
    <string name="main_cash">Main_cash</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml:83: Warning: The resource R.array.categories_array appears to be unused [UnusedResources]
    <string-array name="categories_array">
                  ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\styles.xml:3: Warning: The resource R.style.AppTheme appears to be unused [UnusedResources]
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\work_24px.xml:1: Warning: The resource R.drawable.work_24px appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\logos.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-mdpi\logos.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xhdpi\logos.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xxhdpi\logos.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logos.png: Warning: Launcher icon used as round icon did not have a circular shape [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\logos.png: Warning: Found bitmap drawable res/drawable/logos.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml:53: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml:66: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:107: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:121: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\login.xml:48: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:71: Warning: This text field does not specify an inputType [TextFields]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:94: Warning: This text field does not specify an inputType [TextFields]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml:186: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml:53: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml:66: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:107: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:121: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:71: Warning: Missing autofillHints attribute [Autofill]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:94: Warning: Missing autofillHints attribute [Autofill]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:117: Warning: Missing autofillHints attribute [Autofill]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:150: Warning: Missing autofillHints attribute [Autofill]
               <EditText
                ~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml:186: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:60: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:61: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.jjoe64:graphview:4.2.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:63: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.google.code.gson:gson:2.10.1")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:64: Warning: Use version catalog instead (com.google.firebase:firebase-bom is already available as firebase-bom, but using version 32.1.2 instead) [UseTomlInstead]
    implementation("com.google.firebase:firebase-bom:33.1.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts:65: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("androidx.work:work-runtime:2.8.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\list_bug.xml:8: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\list_item.xml:7: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:130: Warning: Missing contentDescription attribute on image [ContentDescription]
               <ImageView
                ~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:163: Warning: Missing contentDescription attribute on image [ContentDescription]
               <ImageView
                ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:143: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                balanceTextView.text = "0.00"
                                       ~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:143: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                balanceTextView.text = "0.00"
                                        ~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:144: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                targetTextView.text = "0.00"
                                      ~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:144: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                targetTextView.text = "0.00"
                                       ~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:145: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                budgetTextView.text = "0.00"
                                      ~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt:145: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                budgetTextView.text = "0.00"
                                       ~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:333: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    budgetInfoTextView.text = "Total Budget for this month: R$totalBudget"
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:333: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    budgetInfoTextView.text = "Total Budget for this month: R$totalBudget"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:335: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    budgetInfoTextView.text = "Total Budget for this month: R0"
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:335: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    budgetInfoTextView.text = "Total Budget for this month: R0"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:340: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                budgetInfoTextView.text = "Total Budget for this month: R0"
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt:340: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                budgetInfoTextView.text = "Total Budget for this month: R0"
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml:58: Warning: Hardcoded string "Your budget for this month is $0", should use @string resource [HardcodedText]
            android:text="Your budget for this month is $0"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml:42: Warning: Hardcoded string "Create Account", should use @string resource [HardcodedText]
                android:text="Create Account"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\notifications.xml:49: Warning: @id/button can overlap @id/back_button if @id/button grows due to localized text expansion [RelativeOverlap]
        <Button
         ~~~~~~

   Explanation for issues of type "RelativeOverlap":
   If relative layout has text or button items aligned to left and right sides
   they can overlap each other due to localized text expansion unless they
   have mutual constraints like toEndOf/toStartOf.

0 errors, 236 warnings
