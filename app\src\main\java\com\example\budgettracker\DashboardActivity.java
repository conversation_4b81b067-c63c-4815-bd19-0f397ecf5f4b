package com.example.budgettracker;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Color;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;

import android.util.Log;
import android.widget.ImageButton;

import com.jjoe64.graphview.DefaultLabelFormatter;
import com.jjoe64.graphview.GraphView;
import com.jjoe64.graphview.GridLabelRenderer;
import com.jjoe64.graphview.series.LineGraphSeries;
import com.jjoe64.graphview.series.DataPoint;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import retrofit2.Call;
import retrofit2.Response;

public class DashboardActivity extends AppCompatActivity {
    private ApiService apiService;
    private LinearLayout listContainer;
    private RecyclerView recyclerView;
    private ItemAdapter itemAdapter;
    private DatabaseHelper dbHelper;
    public String user_id;
     float totalBudget = 0;
     float balance = 0;
     float trans_target = 0;
    private LoadingSpinner loadingSpinner;
    @SuppressLint("WrongViewCast")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dashboard);
        dbHelper = new DatabaseHelper(this);
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        SessionManager sessionManager = new SessionManager(this);
        TextView  secondTextView = findViewById(R.id.secondTextView);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this,rootLayout);
        Map<String, String> sessionDetails = sessionManager.getAllSessionDetails();
        Log.w("MainActivity", "Session Details: " + sessionDetails.toString());
        if (sessionManager.isLoggedIn()) {
            String displayName = sessionManager.getUserName();
            //Log.w("MainActivityx", "Session Details: " + displayName);
            if (displayName != null) {
                secondTextView.setText(displayName);
            }
            user_id = sessionManager.getUserUid();

        }else{
            Intent intent = new Intent(DashboardActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        }


        setupNavigationButtons();
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        String monthYear = String.format(Locale.getDefault(), "%d-%02d", year, month);

        if (NetworkUtils.isInternetAvailable(this)) {fetchAndSetOnline(monthYear);
        }else {fetchAndSetData(monthYear);}

        calculateBalanceForMonth(monthYear);

        ImageButton  logoutButton = findViewById(R.id.button_logout);
        logoutButton.setOnClickListener(v -> {
            sessionManager.logoutUser();
            Intent intent = new Intent(DashboardActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
        });
    }

    private void setupNavigationButtons() {
        ImageButton homeButton = findViewById(R.id.button_home);
        ImageButton expensesButton = findViewById(R.id.button_expenses);
        ImageButton transactionsButton = findViewById(R.id.button_transactions);
        ImageButton profileButton = findViewById(R.id.button_profile);
        ImageButton NotificationButton = findViewById(R.id.button_notification);


        homeButton.setOnClickListener(v -> navigateTo(DashboardActivity.class));
        expensesButton.setOnClickListener(v -> navigateTo(ExpensesActivity.class));
        transactionsButton.setOnClickListener(v -> navigateTo(TransactionsActivity.class));
        profileButton.setOnClickListener(v -> navigateTo(SettingsActivity.class));
        NotificationButton.setOnClickListener(v -> navigateTo(NotificationActivity.class));
    }

    private void calculateBalanceForMonth(String monthYear) {

        if (NetworkUtils.isInternetAvailable(this)){
            ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
            Call<List<Record>> call = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));

            call.enqueue(new retrofit2.Callback<List<Record>>() {
                @Override
                public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
//                    Log.w("MainActivity", "Session Details: " + response);
                    if (response.isSuccessful() && response.body() != null && !response.body().isEmpty()) {
                        for (Record record : response.body()) {
                            if (record instanceof Transaction) {
                                Transaction transaction = (Transaction) record;
                                totalBudget = (float) transaction.getTotal();
                                balance = (float) transaction.getBalance();
                                trans_target = (float) transaction.getTransTarget();
                                break;
                            }}
                        updateUI();
                    }else{
                        Log.e("TransactionsActivity", "Response not successful or empty.");
                    }
                }
                @Override
                public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                    Toast.makeText(DashboardActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });
        }else {
            BalanceResult result = fetchDataFromLocalDatabase(monthYear);
            balance = result.getBalance();
            totalBudget = result.getTotalBudget();
            trans_target = result.getTransTarget();
            updateUI();

        }
//        Log.e("TransactionsActivity", String.valueOf(balance));
//        Log.e("TransactionsActivity", String.valueOf(totalBudget));
//        Log.e("TransactionsActivity", String.valueOf(trans_target));
//        TextView budgetTextView = findViewById(R.id.budget_id);
//        @SuppressLint("DefaultLocale") String budgetInfo = String.format("$%.2f", balance);
//        budgetTextView.setText(budgetInfo);
//        calculateAndDisplayPercentage(balance, trans_target);
    }

    private void updateUI() {
        TextView budgetTextView = findViewById(R.id.budget_id);
        @SuppressLint("DefaultLocale") String budgetInfo = String.format("$%.2f", balance);
        budgetTextView.setText(budgetInfo);
        calculateAndDisplayPercentage(balance, trans_target);
    }

    private BalanceResult fetchDataFromLocalDatabase(String monthYear) {
        DatabaseHelper dbHelper = new DatabaseHelper(this);
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String transactionsQuery = "SELECT trans_budget, trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?";
        Cursor transactionsCursor = db.rawQuery(transactionsQuery, new String[]{monthYear, String.valueOf(user_id)});

        if (transactionsCursor.moveToFirst()) {
            trans_target = transactionsCursor.getFloat(2);
            totalBudget = transactionsCursor.getFloat(0);
            balance = transactionsCursor.getFloat(1);
        }
        transactionsCursor.close();
        db.close();
        return new BalanceResult(totalBudget, balance, trans_target);
    }

    @SuppressLint("SetTextI18n")
    private void calculateAndDisplayPercentage(float actualAmount, float targetAmount) {
        String result;
        if (targetAmount != 0) {
            float percentageAchieved = (actualAmount / targetAmount) * 100;
            result = String.format(Locale.getDefault(), "%.2f%%", percentageAchieved);
            TextView monthTextView = findViewById(R.id.month);
            if (percentageAchieved >=100){
                monthTextView.setTextColor(Color.GREEN);
            } else if (percentageAchieved >=0) {
                monthTextView.setTextColor(Color.BLUE);
            } else {
                monthTextView.setTextColor(Color.RED);
            }
            monthTextView.setText(result);
        } else {
            TextView monthTextView = findViewById(R.id.month);
            monthTextView.setText("Target not set");
            monthTextView.setTextColor(Color.GRAY);
        }
    }


    private void navigateTo(Class<?> destination) {

        Intent intent = new Intent(DashboardActivity.this, destination);
        startActivity(intent);
        finish();
    }

    // Parse items JSON
    private List<Expenses> parseItems(String json) {
        Gson gson = new Gson();
        Type expensesListType = new TypeToken<List<Expenses>>() {}.getType();
        return gson.fromJson(json, expensesListType);
    }

    // Parse chart data JSON
    private List<ChartData> parseChartData(String json) {
        Gson gson = new Gson();
        Type chartDataListType = new TypeToken<List<ChartData>>() {}.getType();
        return gson.fromJson(json, chartDataListType);
    }


    private void setupChart(List<Integer> amounts) {
        GraphView graph = findViewById(R.id.graph);

        LineGraphSeries<DataPoint> series = new LineGraphSeries<>();
        // Create DataPoints for the chart
        for (int i = 0; i < amounts.size(); i++) {
            series.appendData(new DataPoint(i, amounts.get(i)), true, amounts.size());
        }
        graph.addSeries(series);
        graph.invalidate();
        graph.getGridLabelRenderer().setHorizontalLabelsVisible(true);
        graph.getGridLabelRenderer().setVerticalLabelsVisible(true);
        graph.getGridLabelRenderer().setGridStyle(GridLabelRenderer.GridStyle.NONE);
        graph.setBackgroundColor(Color.TRANSPARENT);
        graph.getViewport().setDrawBorder(true);
        graph.getGridLabelRenderer().setLabelFormatter(new DefaultLabelFormatter() {
            @Override
            public String formatLabel(double value, boolean isValueX) {
                if (isValueX) {
                    return super.formatLabel(value, true); // Default formatting for X axis
                } else {
                    return super.formatLabel(value, false); // Default formatting for Y axis
                }
            }
        });
        graph.getViewport().setScalable(true);
        graph.getViewport().setScrollable(true);

        graph.getViewport().setXAxisBoundsManual(true);
        graph.getViewport().setMinX(0);
        graph.getViewport().setMaxX(amounts.size() - 1);

        graph.getViewport().setYAxisBoundsManual(true);
        graph.getViewport().setMinY(0);
        graph.getViewport().setMaxY(getMaxValue(amounts));
    }
    // Helper method to get max value from the list
    private static double getMaxValue(List<Integer> values) {
        double max = 0;
        for (int value : values) {
            if (value > max) {
                max = value;
            }
        }
        return max;
    }

    private void fetchAndSetData(String monthyear) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String[] projection = {"name","note","date","amount","user_id"};
////        String selection = "user_id = ?";
        String selection = "user_id = ? AND strftime('%Y-%m', date) = ?";
        String[] selectionArgs = {String.valueOf(user_id)};
        Cursor cursor = db.query(
                "expenses",
                projection,
                selection,
                selectionArgs,
                null,
                null,
                null
        );
        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();
        while (cursor.moveToNext()) {
            String name = cursor.getString(cursor.getColumnIndexOrThrow("name"));
            String note = cursor.getString(cursor.getColumnIndexOrThrow("note"));
            String date = cursor.getString(cursor.getColumnIndexOrThrow("date"));
            int amount = cursor.getInt(cursor.getColumnIndexOrThrow("amount"));
            String user_id = String.valueOf(cursor.getInt(cursor.getColumnIndexOrThrow("user_id")));
            Expenses expense = new Expenses(name,date,note,amount,user_id);
            expensesList.add(expense);
            amounts.add(amount);
        }
        cursor.close();
        Gson gson = new Gson();

        String jsonItems = gson.toJson(expensesList);
        if (!amounts.isEmpty()) {
            setupChart(amounts);
        } else {
            GraphView graph = findViewById(R.id.graph);
            graph.removeAllSeries();
        }
        Type listType = new TypeToken<List<Expenses>>() {}.getType();
        List<Expenses> items = gson.fromJson(jsonItems, listType);
        ItemAdapter itemAdapter = new ItemAdapter(items);
        recyclerView.setAdapter(itemAdapter);
    }
    private void fetchAndSetOnline(String monthyear) {
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Call<List<Record>> call = apiService.getRecords("expenses", monthyear, String.valueOf(user_id));
        call.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Expenses> items = getExpenses(response);
                    if (items != null) {
                        for (Expenses expense : items) {expense.setDate(trimDate(expense.getDate()));}
                        ItemAdapter itemAdapter = new ItemAdapter(items);
                        recyclerView.setAdapter(itemAdapter);
                        loadingSpinner.hide();
                    } else {Toast.makeText(getApplicationContext(), "No expenses found", Toast.LENGTH_SHORT).show();}
                } else {
                    loadingSpinner.hide();
                    Toast.makeText(getApplicationContext(), "Failed to fetch data", Toast.LENGTH_SHORT).show();}}
            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {

                Toast.makeText(getApplicationContext(), "Error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                loadingSpinner.hide();
            }

        });

    }
    private String trimDate(String dateTime) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            return outputFormat.format(inputFormat.parse(dateTime));
        } catch (Exception e) {
            e.printStackTrace();
            return dateTime; // Return the original if parsing fails
        }
    }
    private @Nullable List<Expenses> getExpenses(@NonNull Response<List<Record>> response) {
        List<Record> records = response.body();
        List<Expenses> expensesList = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();
        for (Record record : records) {
            if (record instanceof Expenses) {
                Expenses expense = (Expenses) record;
                int  amount = expense.getAmount();
                expensesList.add(expense);
                amounts.add(amount);
            }
        }
        Gson gson = new Gson();
        String jsonItems = gson.toJson(expensesList);
        if (!amounts.isEmpty()) {
            setupChart(amounts);
        } else {
            GraphView graph = findViewById(R.id.graph);
            graph.removeAllSeries();
        }

        Type listType = new TypeToken<List<Expenses>>() {}.getType();
        return gson.fromJson(jsonItems, listType);
    }

}
