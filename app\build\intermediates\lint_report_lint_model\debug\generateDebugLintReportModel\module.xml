<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\BudgetTracker\app"
    name=":app"
    type="APP"
    maven="BudgetTracker:app:unspecified"
    agpVersion="8.5.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="MissingTranslation,MissingPermission"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="MissingPermission"
        severity="IGNORE" />
      <severity
        id="MissingTranslation"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="debug"/>
</lint-module>
