package com.example.budgettracker

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.recyclerview.widget.RecyclerView

class ItemAdapter(private val expensesList: List<Expenses>) : RecyclerView.Adapter<ItemAdapter.ViewHolder>() {

    @NonNull
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.list_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {
        val expense = expensesList[position]
        holder.nameTextView.text = expense.getName()
        holder.dateTextView.text = expense.getDate() // Set the date
        holder.amountTextView.text = expense.getAmount().toString()
    }

    override fun getItemCount(): Int {
        return expensesList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val nameTextView: TextView = itemView.findViewById(R.id.itemName)
        val dateTextView: TextView = itemView.findViewById(R.id.itemDate) // Initialize date TextView
        val amountTextView: TextView = itemView.findViewById(R.id.itemAmount)
    }
}
