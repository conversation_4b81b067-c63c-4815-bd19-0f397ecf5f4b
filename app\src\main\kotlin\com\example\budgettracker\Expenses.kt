package com.example.budgettracker

class Expenses(
    var name: String,
    var date: String,
    var note: String, // Added field for 'note'
    var amount: Int,
    user_id: String
) : Record(user_id) {

    // Getters and Setters
    fun getName(): String = name

    fun setName(name: String) {
        this.name = name
    }

    fun getDate(): String = date

    fun setDate(date: String) {
        this.date = date
    }

    fun getNote(): String = note

    fun setNote(note: String) {
        this.note = note
    }

    fun getAmount(): Int = amount

    fun setAmount(amount: Int) {
        this.amount = amount
    }
}
