package com.example.budgettracker

import android.content.ContentValues
import android.content.Intent
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.appcompat.app.AppCompatActivity
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.gms.tasks.Task
import com.google.firebase.auth.AuthResult
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class RegisterActivity : AppCompatActivity() {

    private lateinit var emailEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var confirmPasswordEditText: EditText
    private lateinit var usernameEdit: EditText
    private lateinit var applyButton: Button
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var mAuth: FirebaseAuth
    private var isPasswordVisible = false
    private lateinit var loadingSpinner: LoadingSpinner

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.register)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        dbHelper = DatabaseHelper(this)
        mAuth = FirebaseAuth.getInstance()

        emailEditText = findViewById(R.id.email_input)
        passwordEditText = findViewById(R.id.password_input)
        confirmPasswordEditText = findViewById(R.id.password_input_confrim)
        usernameEdit = findViewById(R.id.username_input)
        val passwordEyeImageView = findViewById<ImageView>(R.id.password_eye)
        val confirmPasswordEyeImageView = findViewById<ImageView>(R.id.password_eye_confrim)
        applyButton = findViewById(R.id.signup_button)
        
        applyButton.setOnClickListener {
            registerUser()
        }

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@RegisterActivity, MainActivity::class.java)
            startActivity(intent)
        }

        passwordEyeImageView.setOnClickListener { 
            togglePasswordVisibility(passwordEditText, passwordEyeImageView) 
        }
        confirmPasswordEyeImageView.setOnClickListener { 
            togglePasswordVisibility(confirmPasswordEditText, confirmPasswordEyeImageView) 
        }
    }

    private fun togglePasswordVisibility(passwordEditText: EditText, eyeImageView: ImageView) {
        if (isPasswordVisible) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            eyeImageView.setImageResource(R.drawable.eye_close)
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            eyeImageView.setImageResource(R.drawable.eye_icon)
        }
        isPasswordVisible = !isPasswordVisible
        passwordEditText.setSelection(passwordEditText.text.length) // Move cursor to end
    }

    private fun registerUser() {
        val email = emailEditText.text.toString().trim()
        val password = passwordEditText.text.toString().trim()
        val confirmPassword = confirmPasswordEditText.text.toString().trim()
        val username = usernameEdit.text.toString().trim()
        
        when {
            TextUtils.isEmpty(email) -> {
                emailEditText.error = "Email is required"
                return
            }
            TextUtils.isEmpty(username) -> {
                usernameEdit.error = "Username is required"
                return
            }
            TextUtils.isEmpty(password) -> {
                passwordEditText.error = "Password is required"
                return
            }
            password != confirmPassword -> {
                confirmPasswordEditText.error = "Passwords do not match"
                return
            }
        }

        mAuth.createUserWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    val user = mAuth.currentUser
                    if (user != null) {
                        if (NetworkUtils.isInternetAvailable(this)) {
                            saveUserOnline(email, username, user.uid)
                        } else {
                            saveUserData(email, username, user.uid)
                        }
                        Handler().postDelayed({
                            val intent = Intent(this@RegisterActivity, MainActivity::class.java)
                            startActivity(intent)
                            finish()
                        }, 2000)
                    }
                } else {
                    Toast.makeText(
                        this@RegisterActivity,
                        "Registration failed: ${task.exception?.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                    loadingSpinner.hide()
                }
            }
    }

    private fun saveUserData(email: String, username: String, uid: String) {
        val db = dbHelper.writableDatabase
        val values = ContentValues().apply {
            put("user_email", email)
            put("user_name", username)
            put("user_id", uid)
            put("user_status", 0)
        }

        val newRowId = db.insert("user", null, values)
        if (newRowId == -1L) {
            loadingSpinner.hide()
            Toast.makeText(this@RegisterActivity, "Error saving user data", Toast.LENGTH_SHORT).show()
        } else {
            loadingSpinner.hide()
            Toast.makeText(this@RegisterActivity, "User data saved successfully", Toast.LENGTH_SHORT).show()
        }
        db.close()
    }

    private fun saveUserOnline(email: String, username: String, uid: String) {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        
        val data = hashMapOf<String, Any>(
            "user_id" to uid,
            "user_email" to email,
            "user_name" to username,
            "user_status" to 0,
            "created_date" to currentDate
        )
        
        val call = apiService.createRecord("user", data)
        call.enqueue(object : Callback<Void> {
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                if (response.isSuccessful) {
                    Toast.makeText(this@RegisterActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()
                    loadingSpinner.hide()
                } else {
                    Toast.makeText(this@RegisterActivity, "Error saving data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                Toast.makeText(this@RegisterActivity, "Network error. Saving locally.", Toast.LENGTH_SHORT).show()
                saveUserData(email, username, uid)
            }
        })
    }
}
