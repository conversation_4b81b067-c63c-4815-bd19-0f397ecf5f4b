
package com.example.budgettracker;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;


import java.util.List;

public class Item2Adapter extends RecyclerView.Adapter<Item2Adapter.ViewHolder> {

    private List<Budgets> BudgetsList;

    public Item2Adapter(List<Budgets> BudgetsList) {
        this.BudgetsList = BudgetsList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_bug, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Budgets Budgets = BudgetsList.get(position);
        holder.nameTextView.setText(Budgets.getName());
        holder.dateTextView.setText(Budgets.getDate());
        holder.amountTextView.setText(String.valueOf(Budgets.getAmount()));
    }

    @Override
    public int getItemCount() {
        return BudgetsList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView nameTextView;
        public TextView dateTextView;
        public TextView amountTextView;

        public ViewHolder(View itemView) {
            super(itemView);
            nameTextView = itemView.findViewById(R.id.itemName);
            dateTextView = itemView.findViewById(R.id.itemDate);
            amountTextView = itemView.findViewById(R.id.itemAmount);
        }
    }
}

