{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-57:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "6133", "endColumns": "100", "endOffsets": "6229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,6050", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,6128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,199,313,419,509,643,747,858,977,1106,1244,1371,1489,1620,1720,1868,1986,2112,2251,2371,2491,2584,2707,2789,2901,2997,3123", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "194,308,414,504,638,742,853,972,1101,1239,1366,1484,1615,1715,1863,1981,2107,2246,2366,2486,2579,2702,2784,2896,2992,3118,3214"}, "to": {"startLines": "31,32,33,35,36,37,41,42,43,44,45,46,47,48,49,50,51,52,53,56,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2988,3132,3246,3450,3540,3674,4083,4194,4313,4442,4580,4707,4825,4956,5056,5204,5322,5448,5587,5930,6234,6327,6450,6532,6644,6740,6866", "endColumns": "143,113,105,89,133,103,110,118,128,137,126,117,130,99,147,117,125,138,119,119,92,122,81,111,95,125,95", "endOffsets": "3127,3241,3347,3535,3669,3773,4189,4308,4437,4575,4702,4820,4951,5051,5199,5317,5443,5582,5702,6045,6322,6445,6527,6639,6735,6861,6957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "34,38,39,40", "startColumns": "4,4,4,4", "startOffsets": "3352,3778,3875,3984", "endColumns": "97,96,108,98", "endOffsets": "3445,3870,3979,4078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "5707,5813", "endColumns": "105,116", "endOffsets": "5808,5925"}}]}]}