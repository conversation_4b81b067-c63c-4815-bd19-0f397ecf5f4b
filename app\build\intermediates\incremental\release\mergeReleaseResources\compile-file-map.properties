#Mon Jun 09 22:43:14 EAT 2025
com.example.budgettracker.app-main-60\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_attach_money.xml.flat
com.example.budgettracker.app-main-60\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_back.xml.flat
com.example.budgettracker.app-main-60\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_backg_button.xml.flat
com.example.budgettracker.app-main-60\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_background.xml.flat
com.example.budgettracker.app-main-60\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_button_ripple.xml.flat
com.example.budgettracker.app-main-60\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_credit_card.xml.flat
com.example.budgettracker.app-main-60\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_edittext_background.xml.flat
com.example.budgettracker.app-main-60\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_eye_close.xml.flat
com.example.budgettracker.app-main-60\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_eye_icon.xml.flat
com.example.budgettracker.app-main-60\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_fingerprint.xml.flat
com.example.budgettracker.app-main-60\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_home.xml.flat
com.example.budgettracker.app-main-60\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.budgettracker.app-main-60\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.budgettracker.app-main-60\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logos.png.flat
com.example.budgettracker.app-main-60\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_logout.xml.flat
com.example.budgettracker.app-main-60\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mail_24px.xml.flat
com.example.budgettracker.app-main-60\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_monitoring.xml.flat
com.example.budgettracker.app-main-60\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notifications.xml.flat
com.example.budgettracker.app-main-60\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_person.xml.flat
com.example.budgettracker.app-main-60\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_search.xml.flat
com.example.budgettracker.app-main-60\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_settings_24px.xml.flat
com.example.budgettracker.app-main-60\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_shopping.xml.flat
com.example.budgettracker.app-main-60\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_background.xml.flat
com.example.budgettracker.app-main-60\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_normal.xml.flat
com.example.budgettracker.app-main-60\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_press.xml.flat
com.example.budgettracker.app-main-60\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_spinner_select.xml.flat
com.example.budgettracker.app-main-60\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_warning_24px.xml.flat
com.example.budgettracker.app-main-60\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_work_24px.xml.flat
com.example.budgettracker.app-main-60\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_main.xml.flat
com.example.budgettracker.app-main-60\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.budgettracker.app-main-60\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.budgettracker.app-main-60\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_logos.png.flat
com.example.budgettracker.app-main-60\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_logos.png.flat
com.example.budgettracker.app-main-60\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_logos.png.flat
com.example.budgettracker.app-main-60\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_logos.png.flat
com.example.budgettracker.app-main-60\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-60\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_logos.png.flat
com.example.budgettracker.app-main-60\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\navigation_nav_graph.xml.flat
com.example.budgettracker.app-main-60\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.budgettracker.app-main-60\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_main.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_addexpense.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_addtransaction.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/base.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_base.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_content_main.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dashboard.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/language.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_language.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_list_bug.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_list_item.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/login.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_login.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notifications.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/register.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_register.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_settings.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_spinner.xml.flat
com.example.budgettracker.app-mergeReleaseResources-58\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_spinner_item.xml.flat
