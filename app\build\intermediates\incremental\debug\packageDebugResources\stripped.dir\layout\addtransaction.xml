<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_layout"
    android:background="@android:color/white">

    <ImageButton
        android:id="@+id/back_button"
        android:layout_width="55dp"
        android:layout_height="60dp"
        android:layout_marginTop="30dp"
        android:background="@drawable/button_background"
        android:contentDescription="@string/back_description"
        android:src="@drawable/back"
        app:tint="@color/black" />

    <TextView
        android:id="@+id/title"
        android:layout_width="185dp"
        android:layout_height="64dp"
        android:layout_below="@id/back_button"
        android:layout_marginStart="45dp"
        android:layout_marginTop="-62dp"
        android:layout_toEndOf="@+id/back_button"
        android:gravity="center"
        android:text="@string/add_money"
        android:layout_centerHorizontal="true"
        android:textColor="#111418"
        android:textSize="23sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/title"
        android:background="@android:color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="51dp"
        android:layout_below="@id/linearLayout3"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="16dp">

        <TextView
            android:id="@+id/budget_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="4dp"
            android:text="Your budget for this month is $0"
            android:textColor="#637488"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linearLayout5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/linearLayout"
        android:orientation="horizontal"
        android:padding="8dp">

        <TextView
            android:id="@+id/itemNameHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/name_bug"
            android:textColor="#111418"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/itemAmountHeader"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:text="@string/bug_amount"
            android:textColor="#111418"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/itemDateHeader"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:text="@string/date_x"
            android:textColor="#111418"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="387dp"
        android:layout_height="176dp"
        android:layout_below="@id/linearLayout5"
        android:layout_marginTop="-3dp" />

    <EditText
        android:id="@+id/editText5"
        android:layout_width="152dp"
        android:layout_height="48dp"
        android:layout_below="@id/recyclerView"
        android:layout_alignParentStart="true"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/edittext_background"
        android:hint="@string/type"
        android:padding="15dp"
        android:textColor="@color/black"
        android:textSize="13sp" />

    <EditText
        android:id="@+id/editText4"
        android:layout_width="167dp"
        android:layout_height="49dp"
        android:layout_below="@id/recyclerView"
        android:layout_marginStart="44dp"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@id/editText5"
        android:background="@drawable/edittext_background"
        android:hint="@string/_0_00"
        android:padding="13dp"
        android:textColor="@color/black"
        android:textSize="15sp" />

    <Button
        android:id="@+id/save_button"
        android:layout_width="328dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/recyclerView"
        android:layout_marginTop="237dp"
        android:background="@drawable/backg_button"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:padding="12dp"
        android:text="@string/add"
        android:textColor="#FFFFFF"
        android:textSize="19sp"
        android:textStyle="bold" />

    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">
        <include
            layout="@layout/spinner" />
    </RelativeLayout>
</RelativeLayout>
