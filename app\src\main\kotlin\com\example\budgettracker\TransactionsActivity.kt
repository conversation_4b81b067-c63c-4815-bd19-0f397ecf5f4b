package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.widget.*
import androidx.annotation.NonNull
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class TransactionsActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var budgetInfoTextView: TextView
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var sessionManager: SessionManager
    private var user_id: String? = null
    private lateinit var loadingSpinner: LoadingSpinner

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.addbudget)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        
        sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid()
        }

        dbHelper = DatabaseHelper(this)
        recyclerView = findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(this)
        
        if (NetworkUtils.isInternetAvailable(this)) {
            fetchAndSetOnline()
            getTotalBudgetForCurrentOnline()
        } else {
            fetchAndSetData()
        }
        
        budgetInfoTextView = findViewById(R.id.budget_info)

        val saveButton = findViewById<Button>(R.id.save_button)
        val editText5 = findViewById<EditText>(R.id.editText5)
        val editText4 = findViewById<EditText>(R.id.editText4)

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@TransactionsActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        saveButton.setOnClickListener {
            val note = editText5.text.toString()
            val amountString = editText4.text.toString()
            
            if (note.isEmpty() || amountString.isEmpty()) {
                Toast.makeText(this@TransactionsActivity, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            val budgetAmount = try {
                amountString.toInt()
            } catch (e: NumberFormatException) {
                Toast.makeText(this@TransactionsActivity, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (NetworkUtils.isInternetAvailable(this)) {
                saveDataToOnline(note, budgetAmount)
            } else {
                saveDataToDatabase(note, budgetAmount)
            }
        }
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabase(name: String, budgetAmount: Int) {
        val db = dbHelper.writableDatabase
        val values = ContentValues()
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)
        
        val query = "SELECT trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id.toString()))
        
        if (cursor != null && cursor.moveToFirst()) {
            @SuppressLint("Range") 
            val transTarget = cursor.getString(cursor.getColumnIndex("trans_target"))
            cursor.close()
        }

        values.put("bud_name", name)
        values.put("bud_date", currentDate)
        values.put("bud_amount", budgetAmount)
        values.put("user_id", user_id)

        val newRowId = db.insert("budget", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@TransactionsActivity, "Error saving amount", Toast.LENGTH_SHORT).show()
        } else {
            if (NetworkUtils.isInternetAvailable(this)) {
                updateTransactionOnline(monthYear, budgetAmount)
            } else {
                updateTransactionAmounts(db, monthYear, budgetAmount)
            }
            Toast.makeText(this@TransactionsActivity, "Amount Added to your Budget", Toast.LENGTH_SHORT).show()
            val intent = getIntent()
            finish()
            startActivity(intent)
        }
        db.close()
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToOnline(name: String, budgetAmount: Int) {
        loadingSpinner.show()
        val db = dbHelper.writableDatabase
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)
        
        val targetCall = apiService.getRecords("transactions", monthYear, user_id.toString())
        targetCall.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var transTarget = 0.0
                    
                    for (record in records) {
                        if (record is Transaction) {
                            transTarget = record.getTransTarget()
                            break
                        }
                    }

                    val data = hashMapOf<String, Any>(
                        "user_id" to user_id!!,
                        "bud_name" to name,
                        "bud_date" to currentDate,
                        "bud_amount" to budgetAmount
                    )

                    val call = apiService.createRecord("budget", data)
                    call.enqueue(object : Callback<Void> {
                        override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                            if (response.isSuccessful) {
                                updateTransactionOnline(monthYear, budgetAmount)
                                Toast.makeText(this@TransactionsActivity, "Amount Added to your Budget", Toast.LENGTH_SHORT).show()
                                loadingSpinner.hide()
                                val intent = getIntent()
                                finish()
                                startActivity(intent)
                            } else {
                                Toast.makeText(this@TransactionsActivity, "Error saving data", Toast.LENGTH_SHORT).show()
                                loadingSpinner.hide()
                            }
                        }

                        override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                            Toast.makeText(this@TransactionsActivity, "Network error. Saving locally.", Toast.LENGTH_SHORT).show()
                            saveDataToDatabase(name, budgetAmount)
                            loadingSpinner.hide()
                        }
                    })
                } else {
                    Toast.makeText(this@TransactionsActivity, "Failed to fetch target data", Toast.LENGTH_SHORT).show()
                    loadingSpinner.hide()
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Toast.makeText(this@TransactionsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }
        })
    }

    private fun updateTransactionAmounts(db: SQLiteDatabase, monthYear: String, budgetAmount: Int) {
        val updateQuery = "UPDATE transactions SET trans_budget = trans_budget + ? WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?"
        db.execSQL(updateQuery, arrayOf(budgetAmount, monthYear, user_id.toString()))
        
        val cursor = db.rawQuery("SELECT changes() AS affected_rows", null)
        if (cursor.moveToFirst()) {
            val rowsUpdated = cursor.getInt(0)
            if (rowsUpdated == 0) {
                Log.w("DatabaseHelper", "No rows updated in transactions table.")
            }
        }
        cursor.close()
    }

    private fun updateTransactionOnline(monthYear: String, budgetAmount: Int) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val fetchCall = apiService.getRecords("transactions", monthYear, user_id.toString())
        
        fetchCall.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    
                    for (record in records) {
                        if (record is Transaction) {
                            val newBudget = record.getTotal() + budgetAmount
                            val data = hashMapOf<String, Any>(
                                "trans_budget" to newBudget,
                                "user_id" to user_id!!
                            )

                            val updateCall = apiService.updateRecord("transactions", user_id!!, monthYear, data)
                            updateCall.enqueue(object : Callback<Void> {
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    if (response.isSuccessful) {
                                        Log.d("TransactionsActivity", "Transaction budget updated successfully")
                                    } else {
                                        Log.e("TransactionsActivity", "Failed to update transaction budget")
                                    }
                                }

                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                    Log.e("TransactionsActivity", "Network error updating transaction: ${t.message}")
                                }
                            })
                            break
                        }
                    }
                } else {
                    Log.e("TransactionsActivity", "Failed to fetch transactions for update")
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Log.e("TransactionsActivity", "Network error fetching transactions: ${t.message}")
            }
        })
    }

    private fun fetchAndSetData() {
        val db = dbHelper.readableDatabase
        val cursor = db.rawQuery("SELECT * FROM budget WHERE user_id = ?", arrayOf(user_id.toString()))
        
        if (cursor != null) {
            val budgetsList = mutableListOf<Budgets>()
            while (cursor.moveToNext()) {
                val name = cursor.getString(cursor.getColumnIndexOrThrow("bud_name"))
                val date = cursor.getString(cursor.getColumnIndexOrThrow("bud_date"))
                val amount = cursor.getInt(cursor.getColumnIndexOrThrow("bud_amount"))
                val budget = Budgets(name, date, amount, user_id!!)
                budgetsList.add(budget)
            }
            cursor.close()
            
            val gson = Gson()
            val jsonItems = gson.toJson(budgetsList)
            val listType = object : TypeToken<List<Budgets>>() {}.type
            val items: List<Budgets> = gson.fromJson(jsonItems, listType)
            val item2Adapter = Item2Adapter(items)
            recyclerView.adapter = item2Adapter
        }
    }

    private fun fetchAndSetOnline() {
        loadingSpinner.show()
        val sdf = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val currentDate = sdf.format(Date())
        
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("budget", currentDate, user_id.toString())
        
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    val items = mutableListOf<Budgets>()
                    
                    for (record in records) {
                        if (record is Budgets) {
                            items.add(record)
                        }
                    }
                    
                    val item2Adapter = Item2Adapter(items)
                    recyclerView.adapter = item2Adapter
                    loadingSpinner.hide()
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(applicationContext, "Failed to fetch data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(applicationContext, "Error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getTotalBudgetForCurrentOnline() {
        val sdf = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val currentDate = sdf.format(Date())
        
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("budget", currentDate, user_id.toString())
        
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var totalBudget = 0
                    
                    for (record in records) {
                        if (record is Budgets) {
                            totalBudget += record.getAmount()
                        }
                    }
                    
                    budgetInfoTextView.text = "Total Budget for this month: R$totalBudget"
                } else {
                    budgetInfoTextView.text = "Total Budget for this month: R0"
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                budgetInfoTextView.text = "Total Budget for this month: R0"
            }
        })
    }
}
