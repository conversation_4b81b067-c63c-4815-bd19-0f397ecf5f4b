<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="notifications" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\notifications.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/notifications_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="168" endOffset="51"/></Target><Target id="@+id/toolbar" view="RelativeLayout"><Expressions/><location startLine="10" startOffset="4" endLine="61" endOffset="20"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="33" endOffset="39"/></Target><Target id="@+id/button" view="Button"><Expressions/><location startLine="48" startOffset="8" endLine="60" endOffset="16"/></Target><Target id="@+id/clear_all_button" view="Button"><Expressions/><location startLine="151" startOffset="4" endLine="166" endOffset="5"/></Target></Targets></Layout>