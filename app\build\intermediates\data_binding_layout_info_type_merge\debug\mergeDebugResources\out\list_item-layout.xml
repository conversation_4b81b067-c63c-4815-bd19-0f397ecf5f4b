<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="list_item" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\list_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/list_item_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="14"/></Target><Target id="@+id/list_icon" view="ImageView"><Expressions/><location startLine="6" startOffset="4" endLine="11" endOffset="9"/></Target><Target id="@+id/itemName" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="20" endOffset="33"/></Target><Target id="@+id/itemAmount" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="29" endOffset="34"/></Target><Target id="@+id/itemDate" view="TextView"><Expressions/><location startLine="30" startOffset="4" endLine="38" endOffset="34"/></Target></Targets></Layout>