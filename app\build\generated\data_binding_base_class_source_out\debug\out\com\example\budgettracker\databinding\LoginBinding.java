// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LoginBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView description;

  @NonNull
  public final LinearLayout emailContainer;

  @NonNull
  public final EditText emailInput;

  @NonNull
  public final ImageView fingerprintIcon;

  @NonNull
  public final TextView fingerprintInstead;

  @NonNull
  public final Button loginButton;

  @NonNull
  public final TextView orLoginWith;

  @NonNull
  public final LinearLayout passwordContainer;

  @NonNull
  public final ImageView passwordEye;

  @NonNull
  public final EditText passwordInput;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final TextView signupInstead;

  @NonNull
  public final RelativeLayout spinnerContainer;

  @NonNull
  public final ImageView welcomeIcon;

  private LoginBinding(@NonNull RelativeLayout rootView, @NonNull TextView description,
      @NonNull LinearLayout emailContainer, @NonNull EditText emailInput,
      @NonNull ImageView fingerprintIcon, @NonNull TextView fingerprintInstead,
      @NonNull Button loginButton, @NonNull TextView orLoginWith,
      @NonNull LinearLayout passwordContainer, @NonNull ImageView passwordEye,
      @NonNull EditText passwordInput, @NonNull RelativeLayout rootLayout,
      @NonNull TextView signupInstead, @NonNull RelativeLayout spinnerContainer,
      @NonNull ImageView welcomeIcon) {
    this.rootView = rootView;
    this.description = description;
    this.emailContainer = emailContainer;
    this.emailInput = emailInput;
    this.fingerprintIcon = fingerprintIcon;
    this.fingerprintInstead = fingerprintInstead;
    this.loginButton = loginButton;
    this.orLoginWith = orLoginWith;
    this.passwordContainer = passwordContainer;
    this.passwordEye = passwordEye;
    this.passwordInput = passwordInput;
    this.rootLayout = rootLayout;
    this.signupInstead = signupInstead;
    this.spinnerContainer = spinnerContainer;
    this.welcomeIcon = welcomeIcon;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LoginBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.description;
      TextView description = ViewBindings.findChildViewById(rootView, id);
      if (description == null) {
        break missingId;
      }

      id = R.id.email_container;
      LinearLayout emailContainer = ViewBindings.findChildViewById(rootView, id);
      if (emailContainer == null) {
        break missingId;
      }

      id = R.id.email_input;
      EditText emailInput = ViewBindings.findChildViewById(rootView, id);
      if (emailInput == null) {
        break missingId;
      }

      id = R.id.fingerprint_icon;
      ImageView fingerprintIcon = ViewBindings.findChildViewById(rootView, id);
      if (fingerprintIcon == null) {
        break missingId;
      }

      id = R.id.fingerprint_instead;
      TextView fingerprintInstead = ViewBindings.findChildViewById(rootView, id);
      if (fingerprintInstead == null) {
        break missingId;
      }

      id = R.id.login_button;
      Button loginButton = ViewBindings.findChildViewById(rootView, id);
      if (loginButton == null) {
        break missingId;
      }

      id = R.id.or_login_with;
      TextView orLoginWith = ViewBindings.findChildViewById(rootView, id);
      if (orLoginWith == null) {
        break missingId;
      }

      id = R.id.password_container;
      LinearLayout passwordContainer = ViewBindings.findChildViewById(rootView, id);
      if (passwordContainer == null) {
        break missingId;
      }

      id = R.id.password_eye;
      ImageView passwordEye = ViewBindings.findChildViewById(rootView, id);
      if (passwordEye == null) {
        break missingId;
      }

      id = R.id.password_input;
      EditText passwordInput = ViewBindings.findChildViewById(rootView, id);
      if (passwordInput == null) {
        break missingId;
      }

      RelativeLayout rootLayout = (RelativeLayout) rootView;

      id = R.id.signup_instead;
      TextView signupInstead = ViewBindings.findChildViewById(rootView, id);
      if (signupInstead == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      id = R.id.welcome_icon;
      ImageView welcomeIcon = ViewBindings.findChildViewById(rootView, id);
      if (welcomeIcon == null) {
        break missingId;
      }

      return new LoginBinding((RelativeLayout) rootView, description, emailContainer, emailInput,
          fingerprintIcon, fingerprintInstead, loginButton, orLoginWith, passwordContainer,
          passwordEye, passwordInput, rootLayout, signupInstead, spinnerContainer, welcomeIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
