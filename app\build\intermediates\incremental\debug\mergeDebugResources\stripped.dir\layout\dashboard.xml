<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:background="@android:color/white">

    <!-- Header with search button -->
    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="4dp">

        <TextView
            android:id="@+id/firstTextView"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/logo_name"
            android:textColor="@color/primary"
            android:textSize="12sp" />

        <ImageButton
            android:id="@+id/button_notification"
            android:layout_width="10dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:contentDescription="@string/notifications"
            android:src="@drawable/notifications"
            android:background="@drawable/button_background"
            app:tint="@color/black" />
        <TextView
            android:id="@+id/secondTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/user_name"
            android:layout_weight="1"
            android:textColor="@color/primary"
            android:textSize="18sp" />
        <ImageButton
            android:id="@+id/button_logout"
            android:layout_width="10dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:contentDescription="@string/notifications"
            android:src="@drawable/logout"
            android:background="@drawable/button_background"
            app:tint="@color/black" />
    </LinearLayout>

    <!-- Main content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/footer"
        android:layout_below="@+id/linearLayout2"
        android:layout_marginTop="0dp"
        android:orientation="vertical"
        android:padding="4dp"
        tools:ignore="NotSibling">

        <!-- Balance section -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/primary_blue"
                android:padding="16dp"

                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account_balance"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/budget_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_0"
                    android:textColor="#111517"
                    android:textSize="32sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/month"
                        android:textColor="#FFFFFF"
                        android:layout_marginEnd="5dp"/>

                    <TextView
                        android:id="@+id/month"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/_12"
                        android:textColor="#078838" />
                </LinearLayout>
            </LinearLayout>

            <!-- Chart time options -->
            <com.jjoe64.graphview.GraphView
                android:id="@+id/graph"
                android:layout_width="wrap_content"
                android:layout_height="200dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_1d"
                    android:textColor="#647987" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_1w"
                    android:textColor="#647987" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_1m"
                    android:textColor="#647987" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_3m"
                    android:textColor="#647987" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/_1y"
                    android:textColor="#647987" />
            </LinearLayout>
        </LinearLayout>


        <!-- List items -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/root_layout"
            android:layout_marginTop="16dp">

            <!-- List Item 1 -->
            <TextView
                android:id="@+id/itemName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#111517"
                android:gravity="center"
                android:text="@string/heading_list_dash"
                android:textSize="16sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="414dp"
                android:background="@android:color/white"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:padding="8dp"
                android:layout_below="@id/itemName">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="357dp"
                    android:layout_marginTop="16dp" />
            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

    <include
        android:id="@+id/footer"
        layout="@layout/base"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">

        <include
            layout="@layout/spinner" />
    </RelativeLayout>

</RelativeLayout>
