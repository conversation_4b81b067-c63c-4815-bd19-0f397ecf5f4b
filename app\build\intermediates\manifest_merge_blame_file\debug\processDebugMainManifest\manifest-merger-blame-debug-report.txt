1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.budgettracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10    <!-- Permission to use biometric authentication -->
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:5:5-79
11-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:5:22-76
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
14-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
15    <!-- Feature declaration for fingerprint hardware -->
16    <uses-feature
16-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:5-90
17        android:name="android.hardware.fingerprint"
17-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:19-62
18        android:required="false" />
18-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:63-87
19
20    <uses-permission android:name="android.permission.INTERNET" />
20-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:11:5-66
20-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:11:22-64
21    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" /> <!-- suppress DeprecatedClassUsageInspection -->
21-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
21-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
22    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:5-74
22-->[androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:22-71
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:5-68
23-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:22-65
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:5-81
24-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:22-78
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
25-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
26
27    <permission
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-44:19
34        android:allowBackup="true"
34-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:14:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:15:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:16:9-54
40        android:icon="@mipmap/logos"
40-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:17:9-37
41        android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:18:9-41
42        android:roundIcon="@mipmap/logos"
42-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:19:9-42
43        android:supportsRtl="true"
43-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:20:9-35
44        android:theme="@style/Base.Theme.BudgetTracker" >
44-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:21:9-56
45        <activity
45-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:23:9-32:20
46            android:name="com.example.budgettracker.MainActivity"
46-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:24:13-41
47            android:exported="true"
47-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:25:13-36
48            android:theme="@style/Base.Theme.BudgetTracker" >
48-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:26:13-60
49            <intent-filter>
49-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:27:13-31:29
50                <action android:name="android.intent.action.MAIN" />
50-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:28:17-69
50-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:28:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:30:17-77
52-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:30:27-74
53            </intent-filter>
54        </activity>
55        <activity
55-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:33:9-35:39
56            android:name="com.example.budgettracker.DashboardActivity"
56-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:34:13-46
57            android:exported="true" />
57-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:35:13-36
58        <activity
58-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:9-78
59            android:name="com.example.budgettracker.ExpensesActivity"
59-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:19-51
60            android:exported="true" />
60-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:53-76
61        <activity
61-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:9-82
62            android:name="com.example.budgettracker.TransactionsActivity"
62-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:19-55
63            android:exported="true" />
63-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:57-80
64        <activity
64-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:9-77
65            android:name="com.example.budgettracker.ProfileActivity"
65-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:19-50
66            android:exported="true" />
66-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:52-75
67        <activity
67-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:9-78
68            android:name="com.example.budgettracker.SettingsActivity"
68-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:19-51
69            android:exported="true" />
69-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:53-76
70        <activity
70-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:9-78
71            android:name="com.example.budgettracker.LanguageActivity"
71-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:19-51
72            android:exported="true" />
72-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:53-76
73        <activity
73-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:9-82
74            android:name="com.example.budgettracker.NotificationActivity"
74-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:19-55
75            android:exported="true" />
75-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:57-80
76        <activity
76-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:9-78
77            android:name="com.example.budgettracker.RegisterActivity"
77-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:19-51
78            android:exported="true" />
78-->C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:53-76
79        <activity
79-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
80            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
80-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
81            android:excludeFromRecents="true"
81-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
82            android:exported="true"
82-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
83            android:launchMode="singleTask"
83-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
84            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
84-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
85            <intent-filter>
85-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
86                <action android:name="android.intent.action.VIEW" />
86-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
86-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
88-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
89                <category android:name="android.intent.category.BROWSABLE" />
89-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
89-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
90
91                <data
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
92                    android:host="firebase.auth"
92-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
93                    android:path="/"
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
94                    android:scheme="genericidp" />
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
95            </intent-filter>
96        </activity>
97        <activity
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
98            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
99            android:excludeFromRecents="true"
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
100            android:exported="true"
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
101            android:launchMode="singleTask"
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
102            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
103            <intent-filter>
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
104                <action android:name="android.intent.action.VIEW" />
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
105
106                <category android:name="android.intent.category.DEFAULT" />
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
107                <category android:name="android.intent.category.BROWSABLE" />
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
108
109                <data
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
110                    android:host="firebase.auth"
110-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
111                    android:path="/"
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
112                    android:scheme="recaptcha" />
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
113            </intent-filter>
114        </activity>
115
116        <service
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
117            android:name="com.google.firebase.components.ComponentDiscoveryService"
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
118            android:directBootAware="true"
118-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
119            android:exported="false" >
119-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
120            <meta-data
120-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
121                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
123            <meta-data
123-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
124                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
124-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
126            <meta-data
126-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
127                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
127-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
129        </service>
130        <service
130-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
131            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
131-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
132            android:enabled="true"
132-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
133            android:exported="false" >
133-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
134            <meta-data
134-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
135                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
135-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
136                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
137        </service>
138
139        <activity
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
140            android:name="androidx.credentials.playservices.HiddenActivity"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
141            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
142            android:enabled="true"
142-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
143            android:exported="false"
143-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
144            android:fitsSystemWindows="true"
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
145            android:theme="@style/Theme.Hidden" >
145-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
146        </activity>
147
148        <provider
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
149            android:name="com.google.firebase.provider.FirebaseInitProvider"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
150            android:authorities="com.example.budgettracker.firebaseinitprovider"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
151            android:directBootAware="true"
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
152            android:exported="false"
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
153            android:initOrder="100" />
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
154
155        <activity
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
156            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
157            android:excludeFromRecents="true"
157-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
158            android:exported="false"
158-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
159            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
159-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
160        <!--
161            Service handling Google Sign-In user revocation. For apps that do not integrate with
162            Google Sign-In, this service will never be started.
163        -->
164        <service
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
165            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
165-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
166            android:exported="true"
166-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
167            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
167-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
168            android:visibleToInstantApps="true" />
168-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
169
170        <activity
170-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
171            android:name="com.google.android.gms.common.api.GoogleApiActivity"
171-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
172            android:exported="false"
172-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
173-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
174
175        <meta-data
175-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
176            android:name="com.google.android.gms.version"
176-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
177            android:value="@integer/google_play_services_version" />
177-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
178
179        <provider
179-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
180            android:name="androidx.startup.InitializationProvider"
180-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
181            android:authorities="com.example.budgettracker.androidx-startup"
181-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
183            <meta-data
183-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
184                android:name="androidx.work.WorkManagerInitializer"
184-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
185                android:value="androidx.startup" />
185-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
186            <meta-data
186-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.emoji2.text.EmojiCompatInitializer"
187-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
188                android:value="androidx.startup" />
188-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
190-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
191                android:value="androidx.startup" />
191-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
192            <meta-data
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
193                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
194                android:value="androidx.startup" />
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
195        </provider>
196
197        <service
197-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
198            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
198-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
200            android:enabled="@bool/enable_system_alarm_service_default"
200-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
201            android:exported="false" />
201-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
202        <service
202-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
203            android:name="androidx.work.impl.background.systemjob.SystemJobService"
203-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
205            android:enabled="@bool/enable_system_job_service_default"
205-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
206            android:exported="true"
206-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
207            android:permission="android.permission.BIND_JOB_SERVICE" />
207-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
208        <service
208-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
209            android:name="androidx.work.impl.foreground.SystemForegroundService"
209-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
211            android:enabled="@bool/enable_system_foreground_service_default"
211-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
212            android:exported="false" />
212-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
213
214        <receiver
214-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
215            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
215-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
217            android:enabled="true"
217-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
218            android:exported="false" />
218-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
219        <receiver
219-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
220-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
222            android:enabled="false"
222-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
225                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
225-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
225-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
226                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
226-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
226-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
227            </intent-filter>
228        </receiver>
229        <receiver
229-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
230            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
230-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
232            android:enabled="false"
232-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
233            android:exported="false" >
233-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
234            <intent-filter>
234-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
235                <action android:name="android.intent.action.BATTERY_OKAY" />
235-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
235-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
236                <action android:name="android.intent.action.BATTERY_LOW" />
236-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
236-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
237            </intent-filter>
238        </receiver>
239        <receiver
239-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
240-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
245                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
245-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
245-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
246                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
246-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
246-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
247            </intent-filter>
248        </receiver>
249        <receiver
249-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
250            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
250-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
252            android:enabled="false"
252-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
253            android:exported="false" >
253-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
254            <intent-filter>
254-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
255                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
255-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
255-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
256            </intent-filter>
257        </receiver>
258        <receiver
258-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
259            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
259-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
261            android:enabled="false"
261-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
262            android:exported="false" >
262-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
263            <intent-filter>
263-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
264                <action android:name="android.intent.action.BOOT_COMPLETED" />
264-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
264-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
265                <action android:name="android.intent.action.TIME_SET" />
265-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
265-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
266                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
266-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
266-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
267            </intent-filter>
268        </receiver>
269        <receiver
269-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
270-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
272            android:enabled="@bool/enable_system_alarm_service_default"
272-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
275                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
275-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
275-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
276            </intent-filter>
277        </receiver>
278        <receiver
278-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
279            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
279-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
280            android:directBootAware="false"
280-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
281            android:enabled="true"
281-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
282            android:exported="true"
282-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
283            android:permission="android.permission.DUMP" >
283-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
284            <intent-filter>
284-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
285                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
285-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
285-->[androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
286            </intent-filter>
287        </receiver>
288
289        <uses-library
289-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
290            android:name="androidx.window.extensions"
290-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
291            android:required="false" />
291-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
292        <uses-library
292-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
293            android:name="androidx.window.sidecar"
293-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
294            android:required="false" />
294-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
295
296        <service
296-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
297            android:name="androidx.room.MultiInstanceInvalidationService"
297-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
298            android:directBootAware="true"
298-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
299            android:exported="false" />
299-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
300
301        <receiver
301-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
302            android:name="androidx.profileinstaller.ProfileInstallReceiver"
302-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
303            android:directBootAware="false"
303-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
304            android:enabled="true"
304-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
305            android:exported="true"
305-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
306            android:permission="android.permission.DUMP" >
306-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
307            <intent-filter>
307-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
308                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
308-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
308-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
309            </intent-filter>
310            <intent-filter>
310-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
311                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
312            </intent-filter>
313            <intent-filter>
313-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
314                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
314-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
314-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
315            </intent-filter>
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
317                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
318            </intent-filter>
319        </receiver>
320    </application>
321
322</manifest>
