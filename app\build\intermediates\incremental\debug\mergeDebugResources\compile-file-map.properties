#Mon Jun 09 22:35:08 EAT 2025
com.example.budgettracker.app-main-61\:/drawable/attach_money.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_attach_money.xml.flat
com.example.budgettracker.app-main-61\:/drawable/back.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_back.xml.flat
com.example.budgettracker.app-main-61\:/drawable/backg_button.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_backg_button.xml.flat
com.example.budgettracker.app-main-61\:/drawable/button_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/button_ripple.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_ripple.xml.flat
com.example.budgettracker.app-main-61\:/drawable/credit_card.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_credit_card.xml.flat
com.example.budgettracker.app-main-61\:/drawable/edittext_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edittext_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/eye_close.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_eye_close.xml.flat
com.example.budgettracker.app-main-61\:/drawable/eye_icon.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_eye_icon.xml.flat
com.example.budgettracker.app-main-61\:/drawable/fingerprint.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fingerprint.xml.flat
com.example.budgettracker.app-main-61\:/drawable/home.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_home.xml.flat
com.example.budgettracker.app-main-61\:/drawable/ic_launcher_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.budgettracker.app-main-61\:/drawable/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logos.png.flat
com.example.budgettracker.app-main-61\:/drawable/logout.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logout.xml.flat
com.example.budgettracker.app-main-61\:/drawable/mail_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_mail_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/monitoring.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_monitoring.xml.flat
com.example.budgettracker.app-main-61\:/drawable/notifications.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_notifications.xml.flat
com.example.budgettracker.app-main-61\:/drawable/person.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_person.xml.flat
com.example.budgettracker.app-main-61\:/drawable/search.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search.xml.flat
com.example.budgettracker.app-main-61\:/drawable/settings_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_settings_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/shopping.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_shopping.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_background.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_background.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_normal.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_normal.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_press.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_press.xml.flat
com.example.budgettracker.app-main-61\:/drawable/spinner_select.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_select.xml.flat
com.example.budgettracker.app-main-61\:/drawable/warning_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_warning_24px.xml.flat
com.example.budgettracker.app-main-61\:/drawable/work_24px.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_work_24px.xml.flat
com.example.budgettracker.app-main-61\:/menu/menu_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_main.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-hdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-mdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.budgettracker.app-main-61\:/mipmap-xxxhdpi/logos.png=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_logos.png.flat
com.example.budgettracker.app-main-61\:/navigation/nav_graph.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.budgettracker.app-main-61\:/xml/backup_rules.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.budgettracker.app-main-61\:/xml/data_extraction_rules.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/activity_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/addexpense.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_addexpense.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/addtransaction.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_addtransaction.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/base.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_base.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/content_main.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_content_main.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/dashboard.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dashboard.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/language.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_language.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/list_bug.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_list_bug.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/list_item.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_list_item.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/login.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_login.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/notifications.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_notifications.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/register.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_register.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/settings.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_settings.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/spinner.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner.xml.flat
com.example.budgettracker.app-mergeDebugResources-58\:/layout/spinner_item.xml=C\:\\Users\\user\\Desktop\\BudgetTracker\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_item.xml.flat
