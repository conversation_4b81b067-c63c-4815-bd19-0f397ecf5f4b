{"logs": [{"outputFile": "com.example.budgettracker.app-mergeReleaseResources-57:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2960,3049,3125,3182,3238,3304,3382,3463,3551,3639,3717,3794,3868,3947,4057,4147,4239,4331,4432,4506,4588,4689,4739,4822,4888,4980,5067,5129,5193,5256,5329,5452,5565,5669,5777,5838,5898", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2955,3044,3120,3177,3233,3299,3377,3458,3546,3634,3712,3789,3863,3942,4052,4142,4234,4326,4427,4501,4583,4684,4734,4817,4883,4975,5062,5124,5188,5251,5324,5447,5560,5664,5772,5833,5893,5979"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3563,3644,3724,3812,3915,4007,4108,4236,6891,6956,9389,9469,9534,9629,9693,9765,9827,9903,9966,10023,10144,10202,10263,10320,10400,10537,10624,10708,10847,10925,11004,11156,11245,11321,11378,11434,11500,11578,11659,11747,11835,11913,11990,12064,12143,12253,12343,12435,12527,12628,12702,12784,12885,12935,13018,13084,13176,13263,13325,13389,13452,13525,13648,13761,13865,13973,14034,14327", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "319,3639,3719,3807,3910,4002,4103,4231,4315,6951,7048,9464,9529,9624,9688,9760,9822,9898,9961,10018,10139,10197,10258,10315,10395,10532,10619,10703,10842,10920,10999,11151,11240,11316,11373,11429,11495,11573,11654,11742,11830,11908,11985,12059,12138,12248,12338,12430,12522,12623,12697,12779,12880,12930,13013,13079,13171,13258,13320,13384,13447,13520,13643,13756,13860,13968,14029,14089,14408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,14547", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,14624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,119", "endOffsets": "165,285"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3049,3164", "endColumns": "114,119", "endOffsets": "3159,3279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14629", "endColumns": "100", "endOffsets": "14725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,334,451,544,694,803,923,1038,1186,1330,1457,1590,1737,1841,2010,2138,2278,2430,2557,2691,2791,2932,3022,3167,3276,3426", "endColumns": "159,118,116,92,149,108,119,114,147,143,126,132,146,103,168,127,139,151,126,133,99,140,89,144,108,149,112", "endOffsets": "210,329,446,539,689,798,918,1033,1181,1325,1452,1585,1732,1836,2005,2133,2273,2425,2552,2686,2786,2927,3017,3162,3271,3421,3534"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3284,3444,6568,6798,7053,7203,7635,7755,7870,8018,8162,8289,8422,8569,8673,8842,8970,9110,9262,14413,14730,14830,14971,15061,15206,15315,15465", "endColumns": "159,118,116,92,149,108,119,114,147,143,126,132,146,103,168,127,139,151,126,133,99,140,89,144,108,149,112", "endOffsets": "3439,3558,6680,6886,7198,7307,7750,7865,8013,8157,8284,8417,8564,8668,8837,8965,9105,9257,9384,14542,14825,14966,15056,15201,15310,15460,15573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "14094,14205", "endColumns": "110,121", "endOffsets": "14200,14322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\305fe414d5006b1315936043a2188629\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5321", "endColumns": "134", "endOffsets": "5451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6685,7312,7415,7526", "endColumns": "112,102,110,108", "endOffsets": "6793,7410,7521,7630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f944e6341a47ce5ae5325f3246eaddd\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4320,4425,4577,4704,4813,4963,5090,5213,5456,5627,5736,5895,6026,6190,6348,6413,6481", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "4420,4572,4699,4808,4958,5085,5208,5316,5622,5731,5890,6021,6185,6343,6408,6476,6563"}}]}]}