package com.example.budgettracker;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;

import com.example.budgettracker.ApiService;
import com.example.budgettracker.DashboardActivity;
import com.example.budgettracker.LoginRes;
import com.example.budgettracker.RetrofitClient;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.snackbar.Snackbar;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.os.Handler;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Objects;
import java.util.concurrent.Executor;
import com.google.firebase.FirebaseApp;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import android.util.Log;

public class MainActivity extends AppCompatActivity {
    private EditText usernameEditText;
    private EditText passwordEditText;
    private FirebaseAuth mAuth;
    private SessionManager sessionManager;
    private DatabaseHelper dbHelper;
    private boolean isPasswordVisible = false;
    private LoadingSpinner loadingSpinner;
    private String displayname2;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        SyncScheduler.scheduleSync(this);
        if (NetworkUtils.isInternetAvailable(this)) {
            Toast.makeText(MainActivity.this, "Online", Toast.LENGTH_SHORT).show();
        }else{
            Toast.makeText(MainActivity.this, "Offline", Toast.LENGTH_SHORT).show();
        }
//        OneTimeWorkRequest testRequest = new OneTimeWorkRequest.Builder(SyncWorker.class).build();
//        WorkManager.getInstance(this).enqueue(testRequest);

        setContentView(R.layout.login);
        FirebaseApp.initializeApp(this);
        dbHelper = new DatabaseHelper(this);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this, rootLayout);
        mAuth = FirebaseAuth.getInstance();
        sessionManager = new SessionManager(this);

//        displayname = sessionManager.getUserUid();
//        getUserDisplayNameOnline(sessionManager.getUserUid());

        usernameEditText = findViewById(R.id.email_input);
        passwordEditText = findViewById(R.id.password_input);
        Button loginButton = findViewById(R.id.login_button);
        ImageView fingerprintButton = findViewById(R.id.fingerprint_icon);
        ImageView passwordEyeImageView = findViewById(R.id.password_eye);
        loginButton.setOnClickListener(v -> {
          loadingSpinner.show();
            String username = usernameEditText.getText().toString();
            String password = passwordEditText.getText().toString();
            if (!username.isEmpty() && !password.isEmpty()) {
                performLogin(username, password);
            } else {
                Toast.makeText(MainActivity.this, "Please enter username and password", Toast.LENGTH_SHORT).show();
                loadingSpinner.hide();
            }
        });
        TextView fig_tetx1  = findViewById(R.id.fingerprint_instead);
        TextView fig_tetx2  = findViewById(R.id.or_login_with);

        if (sessionManager.isLoggedIn()) {
            fingerprintButton.setVisibility(View.VISIBLE);
            fig_tetx1.setVisibility(View.VISIBLE);
            fig_tetx2.setVisibility(View.VISIBLE);

        } else {
            fingerprintButton.setVisibility(View.GONE);
            fig_tetx1.setVisibility(View.GONE);
            fig_tetx2.setVisibility(View.GONE);
        }

        fingerprintButton.setOnClickListener(v -> startFingerprintAuthentication());
        TextView signUpInsteadTextView = findViewById(R.id.signup_instead);
        signUpInsteadTextView.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, RegisterActivity.class);
            startActivity(intent);
        });
        passwordEyeImageView.setOnClickListener(v -> togglePasswordVisibility(passwordEditText, passwordEyeImageView));
    }
    private void togglePasswordVisibility(EditText passwordEditText, ImageView eyeImageView) {
        if (isPasswordVisible) {
            passwordEditText.setTransformationMethod(PasswordTransformationMethod.getInstance());
            eyeImageView.setImageResource(R.drawable.eye_close);
        } else {
            passwordEditText.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            eyeImageView.setImageResource(R.drawable.eye_icon);
        }
        isPasswordVisible = !isPasswordVisible;
        passwordEditText.setSelection(passwordEditText.getText().length());
    }
//    private void performLogin(String email, String password) {
//        mAuth.signInWithEmailAndPassword(email, password)
//                .addOnCompleteListener(this, task -> {
//                    if (task.isSuccessful()) {
//                        FirebaseUser user = mAuth.getCurrentUser();
//                        if (user != null) {
//                            if (NetworkUtils.isInternetAvailable(this)) {
//                                getUserDisplayNameOnline(user.getUid());
//                            }else{
//                                displayname2 = getUserDisplayName( user.getUid());
//                            }
//                            sessionManager.createLoginSession(email, displayname2, user.getUid());
//                            if (NetworkUtils.isInternetAvailable(this)) {saveUserOnline(user);}else{
//                                saveUserToDatabase(user);}
//
//                                    new Handler().postDelayed(() -> {
//                                        loadingSpinner.hide();
//                                        Intent intent = new Intent(MainActivity.this, DashboardActivity.class);
//                                        startActivity(intent);
//                                        finish();
//                                    }, 2000);
//                            }
//                    } else {
//                        Toast.makeText(MainActivity.this, "Authentication failed.", Toast.LENGTH_SHORT).show();
//                        loadingSpinner.hide();
//                    }
//                });
//        }
//
private void performLogin(String email, String password) {
    mAuth.signInWithEmailAndPassword(email, password)
            .addOnCompleteListener(this, task -> {
                if (task.isSuccessful()) {
                    FirebaseUser user = mAuth.getCurrentUser();
                    if (user != null) {
                        if (NetworkUtils.isInternetAvailable(this)) {
                            getUserDisplayNameOnline(user.getUid(), displayName -> {
                                if (displayName != null) {
                                    displayname2 = displayName;
                                } else {
                                    displayname2 = getUserDisplayName(user.getUid());
                                }
                                sessionManager.createLoginSession(email, displayname2, user.getUid());
                                proceedToDashboard();
                            });
                        } else {
                            displayname2 = getUserDisplayName(user.getUid());
                            sessionManager.createLoginSession(email, displayname2, user.getUid());
                            proceedToDashboard();
                        }
                    }
                } else {
                    Toast.makeText(MainActivity.this, "Authentication failed.", Toast.LENGTH_SHORT).show();
                    loadingSpinner.hide();
                }
            });
}
    private void proceedToDashboard() {
        new Handler().postDelayed(() -> {
            loadingSpinner.hide();
            Intent intent = new Intent(MainActivity.this, DashboardActivity.class);
            startActivity(intent);
            finish();
        }, 2000);
    }

public String getUserDisplayName(String user_id) {
    SQLiteDatabase db = dbHelper.getReadableDatabase();
    String displayName = null;
    String[] projection = { "user_name" };
    String selection = "user_id = ?";
    String[] selectionArgs = { user_id };
    Cursor cursor = db.query(
            "user",
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
    );
    if (cursor.moveToFirst()) {
        displayName = cursor.getString(cursor.getColumnIndexOrThrow("user_name"));
    }
    cursor.close();
    db.close();
    return displayName;
}

//public String getUserDisplayNameOnline(String user_id) {
//    ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
//    Call<List<Record>> call = apiService.getRecords("user", user_id, String.valueOf(user_id));
//
//    call.enqueue(new retrofit2.Callback<List<Record>>() {
//        @Override
//        public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
//            if (response.isSuccessful() && response.body() != null) {
//                List<Record> records = response.body();
//                if (!records.isEmpty()) {
//                    User user = (User) records.get(0);
//                    displayname2 = user.getUserName();
//                    Log.w("MainActivity", "User ID: " + user.getuser_id() + ", User Name: " + displayname2);
//                } else {
//                    Log.w("MainActivity", "No user found");
//                }
//            } else {
//                Log.w("MainActivity", "Failed to fetch username");
//            }
//        }
//
//        @Override
//        public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
//            Log.w("MainActivity", "Network error: " + t.getMessage());
//        }
//    });
//
//    return displayname2;
//}
public void getUserDisplayNameOnline(String user_id, DisplayNameCallback callback) {
    ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
    Call<List<Record>> call = apiService.getRecords("user", user_id, String.valueOf(user_id));

    call.enqueue(new retrofit2.Callback<List<Record>>() {
        @Override
        public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
            if (response.isSuccessful() && response.body() != null) {
                List<Record> records = response.body();
                if (!records.isEmpty()) {
                    User user = (User) records.get(0);
                    displayname2 = user.getUserName();
                    callback.onDisplayNameRetrieved(displayname2);
                    Log.w("MainActivity", "User ID: " + user.getuser_id() + ", User Name: " + displayname2);
                } else {
                    Log.w("MainActivity", "No user found");
                    callback.onDisplayNameRetrieved(null);
                }
            } else {
                Log.w("MainActivity", "Failed to fetch username");
                callback.onDisplayNameRetrieved(null);
            }
        }

        @Override
        public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
            Log.w("MainActivity", "Network error: " + t.getMessage());
            callback.onDisplayNameRetrieved(null);
        }
    });
}
    public interface DisplayNameCallback {
        void onDisplayNameRetrieved(String displayName);
    }

    private void saveUserToDatabase(FirebaseUser user) {

        SQLiteDatabase db = dbHelper.getWritableDatabase();
        String user_id =  user.getUid();
        ContentValues values = new ContentValues();
        values.put("user_status", 2);
        String selection = "user_id = ?";
        String[] selectionArgs = { user_id };
        db.update("user", values, selection, selectionArgs);
        db.close();
    }
    private void saveUserOnline(FirebaseUser user) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        String user_id = user.getUid();
        Map<String, Object> updatedValues = new HashMap<>();
        updatedValues.put("user_status", 2);
        Call<Void> call = apiService.updateRecord("user", user_id, String.valueOf(dateFormat), updatedValues);
        call.enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    Log.d("SaveUser", "User status updated successfully.");
                } else {
                    Log.e("SaveUser", "Error updating user status: " + response.message());
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Log.e("SaveUser", "Network error: " + t.getMessage());
            }
        });
    }

    private void startFingerprintAuthentication() {

        Executor executor = ContextCompat.getMainExecutor(this);
        BiometricPrompt biometricPrompt = new BiometricPrompt(MainActivity.this, executor, new BiometricPrompt.AuthenticationCallback() {
            @Override
            public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                super.onAuthenticationError(errorCode, errString);
                Toast.makeText(MainActivity.this, "Authentication error: " + errString, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                super.onAuthenticationSucceeded(result);
                // Perform login or navigate to the next screen
                Toast.makeText(MainActivity.this, "Authentication succeeded!", Toast.LENGTH_SHORT).show();
                // You can directly perform login or navigate to another activity
                Intent intent = new Intent(MainActivity.this, DashboardActivity.class);
                startActivity(intent);
                finish();

            }

            @Override
            public void onAuthenticationFailed() {
                super.onAuthenticationFailed();
                Toast.makeText(MainActivity.this, "Authentication failed", Toast.LENGTH_SHORT).show();
            }
        });

        BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                .setTitle("Fingerprint Authentication")
                .setSubtitle("Log in using your fingerprint")
                .setNegativeButtonText("Cancel")
                .build();

        biometricPrompt.authenticate(promptInfo);
    }
    }
