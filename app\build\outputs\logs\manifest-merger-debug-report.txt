-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:2:1-46:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e506a13136291a8868d7fe987039ca56\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0399dae3028844ddbb6b353ae7aaba68\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e20a9584fb6d0b81b9648ad2cc2052\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a40f0361d7b414913725e1384bd9c69\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b881c0ea3c286d2d70c39490113a0ee\transformed\navigation-ui-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d752ff60ad5917ff94fc88eb3d6b2b1\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\5fc560d4a9dbb1eb5cb67bff25db2518\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9c9decb2bee20882f04c835d9f9d57\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8219bbaa3d60a06b715cff67a6a92f0c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\23473c78612b7b9e2c038e9a18ee6d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a211846894462ce8f4408948b4523b59\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0628b54f3ccce73188f36881ce28e044\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d938e6328a23a4bf4ba560eb20dece64\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a4bea52eb98ad6992894fd53ea00feb9\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5e4fc8f5e9a7d4ec67566d6565ed06c7\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b9f1ae3957b7127b14f3aa176f95d8\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8e7c3c3372eb70748f6dd0646e6cd63\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a47d93ecc31dae97fbc4a63cf79d833\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\07c9c1cbeae17964822afcaf41e6cedb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5c183edf61cb8c991a818a44c094a58\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\5eaea0f8c65e93257e7905d1a5421722\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed4ae4bdf49abc57b5aaae38e07f6728\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\114c1309b9cdeb7088c68114f734fb53\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a254238875120733babaf0e627a193f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a190c6669d60a33532ca4a4be78607bf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce11ed89ee4ed628b4c659189382915a\transformed\contentpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa59820bc94fbfba9d4fdabf76a9fe49\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b26afb9dc727b4527dddd2244ee38d6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aba3d5556ad0bae1cf2fb452ae56f29d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aabd764c651f295f34566f1cee58b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c211eeab49a9d3aee6eb7d45b9e4e3c0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aaae9cb26fb3d8cc3630680cc8e9b84\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a600bcc3103389458ed5fc1c7dcfc20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\758b9cf70e70042c0bcd36c54b7aba20\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8dec14ecad9b37021530cbc6c8a6660\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a46ec1be81734680df17379bcf1dbdf4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac6f2fa7af122123b162186c15eac1ea\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b7504402ff25b4156870273582a3050\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a28ad2a9b14700ed9fec21239fc9d0a6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5863374ef62e245a16f9a092187c5e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e50ec790bdc162e21e1656b096f3eab\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\732cfd11797931d6f3ef4cd43278b30d\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e6b5e442a304a4c6644356a402463a9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabe18de8b77eca9338f924b3abc2e2e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f7e3d5d239243efc43c714fe9b4be34\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fb1e1560511484c651e775ea8c2b86fd\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f6aeb0514703c8881e965594e233ac1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\11708be0205ee3dee3d282485c59f7de\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\73d3f5054965397560cb3893d50d2b82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d1548ca141623ec7f677b6f58c36de6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d288c0cffd84489bb3c85e0c29d3f2f4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6989877767c350eb899aba00e4385878\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\014f1bc4cbd1f9d4538f5e8192f99c7b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa8a02d7e0898e5609fbf1174d20ebcd\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86b4e9a3eb5a130e29657b3ac3b2ac\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd9e13b85d1663408450fddf17059f96\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\29ed0ad8c67d402e5004b110d55fc5e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fea5d6356fec59dee6eafebfaf6994d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc416eed49be41a86065001f1da7a7f6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49583a072f1e899832e798c0456ab123\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e797031cd3330d56e00ba82204fcee3f\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\108ed475326dd3643041ce8c7c7ab64f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01e82846cd7f3962725502237693d2f6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb9046eaa8321e00a822ff8d60d58524\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\42f5a9f94db2aed8bbdb4233f7e13c31\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab63ce3116c8fa9cd0b800a63382c2d0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef92419c350d5a6f212ddd0caa7ae7c4\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6462db2ed3bbb135a74ec1d6f630ecbd\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e5f1d62b10b7972f9ab7c54708088a8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:22:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:22:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:7:22-69
uses-feature#android.hardware.fingerprint
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:5-90
	android:required
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:63-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:10:19-62
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:11:5-66
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:11:22-64
application
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-44:19
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:13:5-44:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d752ff60ad5917ff94fc88eb3d6b2b1\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d752ff60ad5917ff94fc88eb3d6b2b1\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8219bbaa3d60a06b715cff67a6a92f0c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8219bbaa3d60a06b715cff67a6a92f0c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\23473c78612b7b9e2c038e9a18ee6d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\23473c78612b7b9e2c038e9a18ee6d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b9f1ae3957b7127b14f3aa176f95d8\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b9f1ae3957b7127b14f3aa176f95d8\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8e7c3c3372eb70748f6dd0646e6cd63\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8e7c3c3372eb70748f6dd0646e6cd63\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a47d93ecc31dae97fbc4a63cf79d833\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a47d93ecc31dae97fbc4a63cf79d833\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5c183edf61cb8c991a818a44c094a58\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5c183edf61cb8c991a818a44c094a58\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\5eaea0f8c65e93257e7905d1a5421722\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:11:5-12:19
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\5eaea0f8c65e93257e7905d1a5421722\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:11:5-12:19
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\108ed475326dd3643041ce8c7c7ab64f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\108ed475326dd3643041ce8c7c7ab64f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:20:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:19:9-42
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:17:9-37
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:21:9-56
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:15:9-65
activity#com.example.budgettracker.MainActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:23:9-32:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:26:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:30:27-74
activity#com.example.budgettracker.DashboardActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:35:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:34:13-46
activity#com.example.budgettracker.ExpensesActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:37:19-51
activity#com.example.budgettracker.TransactionsActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:9-82
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:57-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:38:19-55
activity#com.example.budgettracker.ProfileActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:9-77
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:52-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:39:19-50
activity#com.example.budgettracker.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:40:19-51
activity#com.example.budgettracker.LanguageActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:41:19-51
activity#com.example.budgettracker.NotificationActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:9-82
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:57-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:42:19-55
activity#com.example.budgettracker.RegisterActivity
ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:9-78
	android:exported
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:53-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml:43:19-51
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e506a13136291a8868d7fe987039ca56\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\e506a13136291a8868d7fe987039ca56\transformed\jetified-viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0399dae3028844ddbb6b353ae7aaba68\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0399dae3028844ddbb6b353ae7aaba68\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e20a9584fb6d0b81b9648ad2cc2052\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e20a9584fb6d0b81b9648ad2cc2052\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a40f0361d7b414913725e1384bd9c69\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a40f0361d7b414913725e1384bd9c69\transformed\navigation-fragment-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b881c0ea3c286d2d70c39490113a0ee\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b881c0ea3c286d2d70c39490113a0ee\transformed\navigation-ui-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d752ff60ad5917ff94fc88eb3d6b2b1\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d752ff60ad5917ff94fc88eb3d6b2b1\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\5fc560d4a9dbb1eb5cb67bff25db2518\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\5fc560d4a9dbb1eb5cb67bff25db2518\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9c9decb2bee20882f04c835d9f9d57\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9c9decb2bee20882f04c835d9f9d57\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8219bbaa3d60a06b715cff67a6a92f0c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8219bbaa3d60a06b715cff67a6a92f0c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\23473c78612b7b9e2c038e9a18ee6d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\23473c78612b7b9e2c038e9a18ee6d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a211846894462ce8f4408948b4523b59\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a211846894462ce8f4408948b4523b59\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0628b54f3ccce73188f36881ce28e044\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0628b54f3ccce73188f36881ce28e044\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d938e6328a23a4bf4ba560eb20dece64\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d938e6328a23a4bf4ba560eb20dece64\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a4bea52eb98ad6992894fd53ea00feb9\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\a4bea52eb98ad6992894fd53ea00feb9\transformed\jetified-fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5e4fc8f5e9a7d4ec67566d6565ed06c7\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\5e4fc8f5e9a7d4ec67566d6565ed06c7\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b9f1ae3957b7127b14f3aa176f95d8\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\72b9f1ae3957b7127b14f3aa176f95d8\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8e7c3c3372eb70748f6dd0646e6cd63\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8e7c3c3372eb70748f6dd0646e6cd63\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a47d93ecc31dae97fbc4a63cf79d833\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a47d93ecc31dae97fbc4a63cf79d833\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\07c9c1cbeae17964822afcaf41e6cedb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\07c9c1cbeae17964822afcaf41e6cedb\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5c183edf61cb8c991a818a44c094a58\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5c183edf61cb8c991a818a44c094a58\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\5eaea0f8c65e93257e7905d1a5421722\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.jjoe64:graphview:4.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\5eaea0f8c65e93257e7905d1a5421722\transformed\jetified-graphview-4.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed4ae4bdf49abc57b5aaae38e07f6728\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed4ae4bdf49abc57b5aaae38e07f6728\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\114c1309b9cdeb7088c68114f734fb53\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\114c1309b9cdeb7088c68114f734fb53\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a254238875120733babaf0e627a193f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a254238875120733babaf0e627a193f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a190c6669d60a33532ca4a4be78607bf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\a190c6669d60a33532ca4a4be78607bf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce11ed89ee4ed628b4c659189382915a\transformed\contentpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.contentpager:contentpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce11ed89ee4ed628b4c659189382915a\transformed\contentpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa59820bc94fbfba9d4fdabf76a9fe49\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa59820bc94fbfba9d4fdabf76a9fe49\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b26afb9dc727b4527dddd2244ee38d6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b26afb9dc727b4527dddd2244ee38d6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aba3d5556ad0bae1cf2fb452ae56f29d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\aba3d5556ad0bae1cf2fb452ae56f29d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aabd764c651f295f34566f1cee58b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aabd764c651f295f34566f1cee58b40\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c211eeab49a9d3aee6eb7d45b9e4e3c0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c211eeab49a9d3aee6eb7d45b9e4e3c0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aaae9cb26fb3d8cc3630680cc8e9b84\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5aaae9cb26fb3d8cc3630680cc8e9b84\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a600bcc3103389458ed5fc1c7dcfc20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a600bcc3103389458ed5fc1c7dcfc20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\758b9cf70e70042c0bcd36c54b7aba20\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\758b9cf70e70042c0bcd36c54b7aba20\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8dec14ecad9b37021530cbc6c8a6660\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8dec14ecad9b37021530cbc6c8a6660\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a46ec1be81734680df17379bcf1dbdf4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a46ec1be81734680df17379bcf1dbdf4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac6f2fa7af122123b162186c15eac1ea\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac6f2fa7af122123b162186c15eac1ea\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b7504402ff25b4156870273582a3050\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b7504402ff25b4156870273582a3050\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a28ad2a9b14700ed9fec21239fc9d0a6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a28ad2a9b14700ed9fec21239fc9d0a6\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5863374ef62e245a16f9a092187c5e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5863374ef62e245a16f9a092187c5e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e50ec790bdc162e21e1656b096f3eab\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e50ec790bdc162e21e1656b096f3eab\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\732cfd11797931d6f3ef4cd43278b30d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\732cfd11797931d6f3ef4cd43278b30d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e6b5e442a304a4c6644356a402463a9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e6b5e442a304a4c6644356a402463a9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabe18de8b77eca9338f924b3abc2e2e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabe18de8b77eca9338f924b3abc2e2e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f7e3d5d239243efc43c714fe9b4be34\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f7e3d5d239243efc43c714fe9b4be34\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fb1e1560511484c651e775ea8c2b86fd\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\fb1e1560511484c651e775ea8c2b86fd\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f6aeb0514703c8881e965594e233ac1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8f6aeb0514703c8881e965594e233ac1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\11708be0205ee3dee3d282485c59f7de\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\11708be0205ee3dee3d282485c59f7de\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\73d3f5054965397560cb3893d50d2b82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\73d3f5054965397560cb3893d50d2b82\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d1548ca141623ec7f677b6f58c36de6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d1548ca141623ec7f677b6f58c36de6\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d288c0cffd84489bb3c85e0c29d3f2f4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d288c0cffd84489bb3c85e0c29d3f2f4\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6989877767c350eb899aba00e4385878\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\6989877767c350eb899aba00e4385878\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\014f1bc4cbd1f9d4538f5e8192f99c7b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\014f1bc4cbd1f9d4538f5e8192f99c7b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa8a02d7e0898e5609fbf1174d20ebcd\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa8a02d7e0898e5609fbf1174d20ebcd\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86b4e9a3eb5a130e29657b3ac3b2ac\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86b4e9a3eb5a130e29657b3ac3b2ac\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd9e13b85d1663408450fddf17059f96\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd9e13b85d1663408450fddf17059f96\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\29ed0ad8c67d402e5004b110d55fc5e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\29ed0ad8c67d402e5004b110d55fc5e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fea5d6356fec59dee6eafebfaf6994d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fea5d6356fec59dee6eafebfaf6994d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc416eed49be41a86065001f1da7a7f6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc416eed49be41a86065001f1da7a7f6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49583a072f1e899832e798c0456ab123\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49583a072f1e899832e798c0456ab123\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e797031cd3330d56e00ba82204fcee3f\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e797031cd3330d56e00ba82204fcee3f\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\108ed475326dd3643041ce8c7c7ab64f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\108ed475326dd3643041ce8c7c7ab64f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01e82846cd7f3962725502237693d2f6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\01e82846cd7f3962725502237693d2f6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb9046eaa8321e00a822ff8d60d58524\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb9046eaa8321e00a822ff8d60d58524\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\42f5a9f94db2aed8bbdb4233f7e13c31\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\42f5a9f94db2aed8bbdb4233f7e13c31\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab63ce3116c8fa9cd0b800a63382c2d0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab63ce3116c8fa9cd0b800a63382c2d0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef92419c350d5a6f212ddd0caa7ae7c4\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ef92419c350d5a6f212ddd0caa7ae7c4\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6462db2ed3bbb135a74ec1d6f630ecbd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6462db2ed3bbb135a74ec1d6f630ecbd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e5f1d62b10b7972f9ab7c54708088a8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8e5f1d62b10b7972f9ab7c54708088a8\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\AndroidManifest.xml
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\32eb9f7cc3c54babe7366b4098b76fa2\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cabdb72f14db89437a048f32585be859\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\34c45124170b8a85514bd5a4e5c2ec60\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\35f5969e373b535a80b2c42a8b72ed9f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\91cb95d865b1907351660eac7ec9d395\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.2.0-alpha05] C:\Users\<USER>\.gradle\caches\transforms-4\6d4c67547e624b6cccfef70d7d267bfc\transformed\biometric-1.2.0-alpha05\AndroidManifest.xml:25:22-71
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd27229091d81bb2cd0f319ca0d1dd3b\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6f944e6341a47ce5ae5325f3246eaddd\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\305fe414d5006b1315936043a2188629\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:23:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\da35ffc2b5961a40249b0922ab01a6c6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\c6cb98f356c47ec463fb632bb6d83df0\transformed\work-runtime-2.8.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7de1b49ebabd8ccdd2be43b4477e127\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb2ff24806fc6df8241ced12f235c576\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.budgettracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\41c0f31cef78f5453d5fb29f83ccc123\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\749385e61af40365024667155cacc271\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fe40a4ca45d9a7766520188e1305b16\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\afe6859dba4131d89f57ba3906cb84d2\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
