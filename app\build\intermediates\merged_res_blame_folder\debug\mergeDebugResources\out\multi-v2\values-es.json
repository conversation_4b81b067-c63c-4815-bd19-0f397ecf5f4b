{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-57:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,174", "endColumns": "118,119", "endOffsets": "169,289"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "14105,14224", "endColumns": "118,119", "endOffsets": "14219,14339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6762,7373,7474,7589", "endColumns": "106,100,114,104", "endOffsets": "6864,7469,7584,7689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14643", "endColumns": "100", "endOffsets": "14739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3565,3646,3725,3812,3913,4009,4113,4235,6963,7028,9453,9534,9597,9686,9750,9819,9882,9956,10020,10076,10194,10252,10314,10370,10450,10589,10678,10760,10901,10982,11062,11213,11303,11383,11439,11495,11561,11640,11722,11810,11899,11973,12050,12120,12199,12299,12383,12467,12559,12659,12733,12814,12916,12969,13054,13121,13214,13303,13365,13429,13492,13560,13673,13780,13884,13985,14045,14344", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3641,3720,3807,3908,4004,4108,4230,4311,7023,7118,9529,9592,9681,9745,9814,9877,9951,10015,10071,10189,10247,10309,10365,10445,10584,10673,10755,10896,10977,11057,11208,11298,11378,11434,11490,11556,11635,11717,11805,11894,11968,12045,12115,12194,12294,12378,12462,12554,12654,12728,12809,12911,12964,13049,13116,13209,13298,13360,13424,13487,13555,13668,13775,13879,13980,14040,14100,14422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3059,3173", "endColumns": "113,121", "endOffsets": "3168,3290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\305fe414d5006b1315936043a2188629\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5352", "endColumns": "159", "endOffsets": "5507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,14560", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,325,434,528,673,778,896,1016,1159,1309,1455,1591,1737,1837,1999,2121,2267,2409,2537,2670,2763,2893,2976,3096,3196,3333", "endColumns": "154,114,108,93,144,104,117,119,142,149,145,135,145,99,161,121,145,141,127,132,92,129,82,119,99,136,104", "endOffsets": "205,320,429,523,668,773,891,1011,1154,1304,1450,1586,1732,1832,1994,2116,2262,2404,2532,2665,2758,2888,2971,3091,3191,3328,3433"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3295,3450,6653,6869,7123,7268,7694,7812,7932,8075,8225,8371,8507,8653,8753,8915,9037,9183,9325,14427,14744,14837,14967,15050,15170,15270,15407", "endColumns": "154,114,108,93,144,104,117,119,142,149,145,135,145,99,161,121,145,141,127,132,92,129,82,119,99,136,104", "endOffsets": "3445,3560,6757,6958,7263,7368,7807,7927,8070,8220,8366,8502,8648,8748,8910,9032,9178,9320,9448,14555,14832,14962,15045,15165,15265,15402,15507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f944e6341a47ce5ae5325f3246eaddd\\transformed\\jetified-play-services-base-18.0.1\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4316,4424,4587,4718,4826,4987,5120,5242,5512,5704,5813,5978,6110,6275,6432,6499,6568", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4419,4582,4713,4821,4982,5115,5237,5347,5699,5808,5973,6105,6270,6427,6494,6563,6648"}}]}]}