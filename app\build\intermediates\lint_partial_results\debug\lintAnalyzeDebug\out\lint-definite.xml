<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="incidents">

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1024"
            endOffset="1314"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1900"
            endOffset="2190"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="947"
            endOffset="1237"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="9"
            column="25"
            startOffset="315"
            endLine="9"
            endColumn="1413"
            endOffset="1703"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="53"
            column="6"
            startOffset="1983"
            endLine="53"
            endColumn="14"
            endOffset="1991"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="53"
            column="6"
            startOffset="1983"
            endLine="53"
            endColumn="14"
            endOffset="1991"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="66"
            column="6"
            startOffset="2450"
            endLine="66"
            endColumn="14"
            endOffset="2458"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="66"
            column="6"
            startOffset="2450"
            endLine="66"
            endColumn="14"
            endOffset="2458"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Your budget for this month is $0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="58"
            column="13"
            startOffset="2139"
            endLine="58"
            endColumn="60"
            endOffset="2186"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="107"
            column="6"
            startOffset="3862"
            endLine="107"
            endColumn="14"
            endOffset="3870"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="107"
            column="6"
            startOffset="3862"
            endLine="107"
            endColumn="14"
            endOffset="3870"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="121"
            column="6"
            startOffset="4380"
            endLine="121"
            endColumn="14"
            endOffset="4388"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="121"
            column="6"
            startOffset="4380"
            endLine="121"
            endColumn="14"
            endOffset="4388"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_bug.xml"
            line="8"
            column="6"
            startOffset="270"
            endLine="8"
            endColumn="15"
            endOffset="279"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item.xml"
            line="7"
            column="6"
            startOffset="268"
            endLine="7"
            endColumn="15"
            endOffset="277"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="48"
            column="10"
            startOffset="1779"
            endLine="48"
            endColumn="18"
            endOffset="1787"/>
    </incident>

    <incident
        id="RelativeOverlap"
        severity="warning"
        message="`@id/button` can overlap `@id/back_button` if @id/button grows due to localized text expansion">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="49"
            column="10"
            startOffset="1999"
            endLine="49"
            endColumn="16"
            endOffset="2005"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Create Account&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="42"
            column="17"
            startOffset="1747"
            endLine="42"
            endColumn="46"
            endOffset="1776"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="64"
            column="16"
            startOffset="2482"
            endLine="64"
            endColumn="54"
            endOffset="2520"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="71"
            column="17"
            startOffset="2790"
            endLine="71"
            endColumn="25"
            endOffset="2798"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="71"
            column="17"
            startOffset="2790"
            endLine="71"
            endColumn="25"
            endOffset="2798"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="87"
            column="16"
            startOffset="3497"
            endLine="87"
            endColumn="54"
            endOffset="3535"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="94"
            column="17"
            startOffset="3805"
            endLine="94"
            endColumn="25"
            endOffset="3813"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="94"
            column="17"
            startOffset="3805"
            endLine="94"
            endColumn="25"
            endOffset="3813"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="117"
            column="17"
            startOffset="4804"
            endLine="117"
            endColumn="25"
            endOffset="4812"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="130"
            column="17"
            startOffset="5393"
            endLine="130"
            endColumn="26"
            endOffset="5402"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="150"
            column="17"
            startOffset="6307"
            endLine="150"
            endColumn="25"
            endOffset="6315"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="163"
            column="17"
            startOffset="6906"
            endLine="163"
            endColumn="26"
            endOffset="6915"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="186"
            column="14"
            startOffset="7345"
            endLine="186"
            endColumn="22"
            endOffset="7353"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="186"
            column="14"
            startOffset="7345"
            endLine="186"
            endColumn="22"
            endOffset="7353"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `185dp` for better performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="237"
            column="9"
            startOffset="9353"
            endLine="237"
            endColumn="37"
            endOffset="9381"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="4855"
                endOffset="4864"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="120"
            column="41"
            startOffset="4855"
            endLine="120"
            endColumn="49"
            endOffset="4863"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="4890"
                endOffset="4899"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="120"
            column="76"
            startOffset="4890"
            endLine="120"
            endColumn="84"
            endOffset="4898"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="5718"
                endOffset="5727"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="136"
            column="40"
            startOffset="5718"
            endLine="136"
            endColumn="48"
            endOffset="5726"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="5753"
                endOffset="5762"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="136"
            column="75"
            startOffset="5753"
            endLine="136"
            endColumn="83"
            endOffset="5761"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="12396"
                endOffset="12405"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="311"
            column="37"
            startOffset="12396"
            endLine="311"
            endColumn="45"
            endOffset="12404"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="12431"
                endOffset="12440"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="311"
            column="72"
            startOffset="12431"
            endLine="311"
            endColumn="80"
            endOffset="12439"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="13349"
                endOffset="13358"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="330"
            column="36"
            startOffset="13349"
            endLine="330"
            endColumn="44"
            endOffset="13357"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="13384"
                endOffset="13393"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="330"
            column="71"
            startOffset="13384"
            endLine="330"
            endColumn="79"
            endOffset="13392"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
                startOffset="13622"
                endOffset="13631"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/DashboardActivity.kt"
            line="338"
            column="29"
            startOffset="13622"
            endLine="338"
            endColumn="37"
            endOffset="13630"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="5327"
                endOffset="5336"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="128"
            column="45"
            startOffset="5327"
            endLine="128"
            endColumn="53"
            endOffset="5335"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="5354"
                endOffset="5363"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="128"
            column="72"
            startOffset="5354"
            endLine="128"
            endColumn="80"
            endOffset="5362"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="6135"
                endOffset="6144"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="142"
            column="44"
            startOffset="6135"
            endLine="142"
            endColumn="52"
            endOffset="6143"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="6162"
                endOffset="6171"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="142"
            column="71"
            startOffset="6162"
            endLine="142"
            endColumn="79"
            endOffset="6170"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="8766"
                endOffset="8775"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="204"
            column="37"
            startOffset="8766"
            endLine="204"
            endColumn="45"
            endOffset="8774"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="8801"
                endOffset="8810"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="204"
            column="72"
            startOffset="8801"
            endLine="204"
            endColumn="80"
            endOffset="8809"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="9638"
                endOffset="9647"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="219"
            column="57"
            startOffset="9638"
            endLine="219"
            endColumn="65"
            endOffset="9646"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="9665"
                endOffset="9674"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="219"
            column="84"
            startOffset="9665"
            endLine="219"
            endColumn="92"
            endOffset="9673"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="10156"
                endOffset="10165"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="227"
            column="56"
            startOffset="10156"
            endLine="227"
            endColumn="64"
            endOffset="10164"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="10183"
                endOffset="10192"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="227"
            column="83"
            startOffset="10183"
            endLine="227"
            endColumn="91"
            endOffset="10191"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="10859"
                endOffset="10868"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="244"
            column="36"
            startOffset="10859"
            endLine="244"
            endColumn="44"
            endOffset="10867"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                startOffset="10894"
                endOffset="10903"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
            line="244"
            column="71"
            startOffset="10894"
            endLine="244"
            endColumn="79"
            endOffset="10902"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
                startOffset="357"
                endOffset="365"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
            line="12"
            column="5"
            startOffset="357"
            endLine="12"
            endColumn="13"
            endOffset="365"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewGroup` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
                startOffset="402"
                endOffset="411"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
            line="13"
            column="37"
            startOffset="402"
            endLine="13"
            endColumn="45"
            endOffset="410"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
                startOffset="642"
                endOffset="651"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/Item2Adapter.kt"
            line="19"
            column="35"
            startOffset="642"
            endLine="19"
            endColumn="43"
            endOffset="650"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
                startOffset="357"
                endOffset="365"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
            line="12"
            column="5"
            startOffset="357"
            endLine="12"
            endColumn="13"
            endOffset="365"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewGroup` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
                startOffset="402"
                endOffset="411"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
            line="13"
            column="37"
            startOffset="402"
            endLine="13"
            endColumn="45"
            endOffset="410"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
                startOffset="643"
                endOffset="652"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ItemAdapter.kt"
            line="19"
            column="35"
            startOffset="643"
            endLine="19"
            endColumn="43"
            endOffset="651"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="6995"
                endOffset="7004"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="168"
            column="37"
            startOffset="6995"
            endLine="168"
            endColumn="45"
            endOffset="7003"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="7030"
                endOffset="7039"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="168"
            column="72"
            startOffset="7030"
            endLine="168"
            endColumn="80"
            endOffset="7038"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="7931"
                endOffset="7940"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="186"
            column="36"
            startOffset="7931"
            endLine="186"
            endColumn="44"
            endOffset="7939"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="7966"
                endOffset="7975"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="186"
            column="71"
            startOffset="7966"
            endLine="186"
            endColumn="79"
            endOffset="7974"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="9136"
                endOffset="9145"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="217"
            column="37"
            startOffset="9136"
            endLine="217"
            endColumn="45"
            endOffset="9144"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="9163"
                endOffset="9172"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="217"
            column="64"
            startOffset="9163"
            endLine="217"
            endColumn="72"
            endOffset="9171"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="9504"
                endOffset="9513"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="225"
            column="36"
            startOffset="9504"
            endLine="225"
            endColumn="44"
            endOffset="9512"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="9531"
                endOffset="9540"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="225"
            column="63"
            startOffset="9531"
            endLine="225"
            endColumn="71"
            endOffset="9539"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `CharSequence` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="9963"
                endOffset="9972"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="234"
            column="64"
            startOffset="9963"
            endLine="234"
            endColumn="72"
            endOffset="9971"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `BiometricPrompt.AuthenticationResult` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
                startOffset="10244"
                endOffset="10253"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/MainActivity.kt"
            line="239"
            column="52"
            startOffset="10244"
            endLine="239"
            endColumn="60"
            endOffset="10252"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
                startOffset="3212"
                endOffset="3221"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
            line="84"
            column="37"
            startOffset="3212"
            endLine="84"
            endColumn="45"
            endOffset="3220"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
                startOffset="3247"
                endOffset="3256"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
            line="84"
            column="72"
            startOffset="3247"
            endLine="84"
            endColumn="80"
            endOffset="3255"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
                startOffset="5076"
                endOffset="5085"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
            line="116"
            column="36"
            startOffset="5076"
            endLine="116"
            endColumn="44"
            endOffset="5084"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
                startOffset="5111"
                endOffset="5120"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationActivity.kt"
            line="116"
            column="71"
            startOffset="5111"
            endLine="116"
            endColumn="79"
            endOffset="5119"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
                startOffset="7021"
                endOffset="7030"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
            line="177"
            column="37"
            startOffset="7021"
            endLine="177"
            endColumn="45"
            endOffset="7029"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
                startOffset="7048"
                endOffset="7057"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
            line="177"
            column="64"
            startOffset="7048"
            endLine="177"
            endColumn="72"
            endOffset="7056"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
                startOffset="7483"
                endOffset="7492"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
            line="186"
            column="36"
            startOffset="7483"
            endLine="186"
            endColumn="44"
            endOffset="7491"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
                startOffset="7510"
                endOffset="7519"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/RegisterActivity.kt"
            line="186"
            column="63"
            startOffset="7510"
            endLine="186"
            endColumn="71"
            endOffset="7518"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="143"
            column="40"
            startOffset="5654"
            endLine="143"
            endColumn="46"
            endOffset="5660"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="143"
            column="41"
            startOffset="5655"
            endLine="143"
            endColumn="45"
            endOffset="5659"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="144"
            column="39"
            startOffset="5699"
            endLine="144"
            endColumn="45"
            endOffset="5705"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="144"
            column="40"
            startOffset="5700"
            endLine="144"
            endColumn="44"
            endOffset="5704"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="145"
            column="39"
            startOffset="5744"
            endLine="145"
            endColumn="45"
            endOffset="5750"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="145"
            column="40"
            startOffset="5745"
            endLine="145"
            endColumn="44"
            endOffset="5749"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="6676"
                endOffset="6685"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="167"
            column="45"
            startOffset="6676"
            endLine="167"
            endColumn="53"
            endOffset="6684"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="6711"
                endOffset="6720"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="167"
            column="80"
            startOffset="6711"
            endLine="167"
            endColumn="88"
            endOffset="6719"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="8264"
                endOffset="8273"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="195"
            column="44"
            startOffset="8264"
            endLine="195"
            endColumn="52"
            endOffset="8272"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="8299"
                endOffset="8308"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="195"
            column="79"
            startOffset="8299"
            endLine="195"
            endColumn="87"
            endOffset="8307"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `KeyEvent` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="8799"
                endOffset="8808"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="209"
            column="42"
            startOffset="8799"
            endLine="209"
            endColumn="50"
            endOffset="8807"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="11369"
                endOffset="11378"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="271"
            column="37"
            startOffset="11369"
            endLine="271"
            endColumn="45"
            endOffset="11377"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="11404"
                endOffset="11413"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="271"
            column="72"
            startOffset="11404"
            endLine="271"
            endColumn="80"
            endOffset="11412"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="12769"
                endOffset="12778"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="299"
            column="36"
            startOffset="12769"
            endLine="299"
            endColumn="44"
            endOffset="12777"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="12804"
                endOffset="12813"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="299"
            column="71"
            startOffset="12804"
            endLine="299"
            endColumn="79"
            endOffset="12812"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="14817"
                endOffset="14826"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="353"
            column="37"
            startOffset="14817"
            endLine="353"
            endColumn="45"
            endOffset="14825"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="14844"
                endOffset="14853"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="353"
            column="64"
            startOffset="14844"
            endLine="353"
            endColumn="72"
            endOffset="14852"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="15454"
                endOffset="15463"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="365"
            column="36"
            startOffset="15454"
            endLine="365"
            endColumn="44"
            endOffset="15462"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="15481"
                endOffset="15490"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="365"
            column="63"
            startOffset="15481"
            endLine="365"
            endColumn="71"
            endOffset="15489"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="16196"
                endOffset="16205"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="382"
            column="37"
            startOffset="16196"
            endLine="382"
            endColumn="45"
            endOffset="16204"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="16223"
                endOffset="16232"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="382"
            column="64"
            startOffset="16223"
            endLine="382"
            endColumn="72"
            endOffset="16231"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="16713"
                endOffset="16722"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="392"
            column="36"
            startOffset="16713"
            endLine="392"
            endColumn="44"
            endOffset="16721"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
                startOffset="16740"
                endOffset="16749"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="392"
            column="63"
            startOffset="16740"
            endLine="392"
            endColumn="71"
            endOffset="16748"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Result` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="699"
                endOffset="707"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="24"
            column="5"
            startOffset="699"
            endLine="24"
            endColumn="13"
            endOffset="707"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="3623"
                endOffset="3632"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="98"
            column="37"
            startOffset="3623"
            endLine="98"
            endColumn="45"
            endOffset="3631"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="3650"
                endOffset="3659"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="98"
            column="64"
            startOffset="3650"
            endLine="98"
            endColumn="72"
            endOffset="3658"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="3929"
                endOffset="3938"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="106"
            column="36"
            startOffset="3929"
            endLine="106"
            endColumn="44"
            endOffset="3937"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="3956"
                endOffset="3965"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="106"
            column="63"
            startOffset="3956"
            endLine="106"
            endColumn="71"
            endOffset="3964"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Context` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="432"
                endOffset="441"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="17"
            column="5"
            startOffset="432"
            endLine="17"
            endColumn="13"
            endOffset="440"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `WorkerParameters` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
                startOffset="463"
                endOffset="472"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SyncWorker.kt"
            line="18"
            column="5"
            startOffset="463"
            endLine="18"
            endColumn="13"
            endOffset="471"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="5541"
                endOffset="5550"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="141"
            column="37"
            startOffset="5541"
            endLine="141"
            endColumn="45"
            endOffset="5549"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="5576"
                endOffset="5585"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="141"
            column="72"
            startOffset="5576"
            endLine="141"
            endColumn="80"
            endOffset="5584"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="6508"
                endOffset="6517"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="162"
            column="49"
            startOffset="6508"
            endLine="162"
            endColumn="57"
            endOffset="6516"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="6535"
                endOffset="6544"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="162"
            column="76"
            startOffset="6535"
            endLine="162"
            endColumn="84"
            endOffset="6543"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="7365"
                endOffset="7374"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="176"
            column="48"
            startOffset="7365"
            endLine="176"
            endColumn="56"
            endOffset="7373"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="7392"
                endOffset="7401"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="176"
            column="75"
            startOffset="7392"
            endLine="176"
            endColumn="83"
            endOffset="7400"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="7969"
                endOffset="7978"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="188"
            column="36"
            startOffset="7969"
            endLine="188"
            endColumn="44"
            endOffset="7977"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="8004"
                endOffset="8013"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="188"
            column="71"
            startOffset="8004"
            endLine="188"
            endColumn="79"
            endOffset="8012"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="9244"
                endOffset="9253"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="214"
            column="37"
            startOffset="9244"
            endLine="214"
            endColumn="45"
            endOffset="9252"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="9279"
                endOffset="9288"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="214"
            column="72"
            startOffset="9279"
            endLine="214"
            endColumn="80"
            endOffset="9287"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="10097"
                endOffset="10106"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="228"
            column="57"
            startOffset="10097"
            endLine="228"
            endColumn="65"
            endOffset="10105"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="10124"
                endOffset="10133"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="228"
            column="84"
            startOffset="10124"
            endLine="228"
            endColumn="92"
            endOffset="10132"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="10621"
                endOffset="10630"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="236"
            column="56"
            startOffset="10621"
            endLine="236"
            endColumn="64"
            endOffset="10629"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="10648"
                endOffset="10657"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="236"
            column="83"
            startOffset="10648"
            endLine="236"
            endColumn="91"
            endOffset="10656"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="11124"
                endOffset="11133"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="248"
            column="36"
            startOffset="11124"
            endLine="248"
            endColumn="44"
            endOffset="11132"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="11159"
                endOffset="11168"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="248"
            column="71"
            startOffset="11159"
            endLine="248"
            endColumn="79"
            endOffset="11167"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="12855"
                endOffset="12864"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="287"
            column="37"
            startOffset="12855"
            endLine="287"
            endColumn="45"
            endOffset="12863"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="12890"
                endOffset="12899"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="287"
            column="72"
            startOffset="12890"
            endLine="287"
            endColumn="80"
            endOffset="12898"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="13745"
                endOffset="13754"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="307"
            column="36"
            startOffset="13745"
            endLine="307"
            endColumn="44"
            endOffset="13753"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="13780"
                endOffset="13789"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="307"
            column="71"
            startOffset="13780"
            endLine="307"
            endColumn="79"
            endOffset="13788"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="14415"
                endOffset="14424"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="322"
            column="37"
            startOffset="14415"
            endLine="322"
            endColumn="45"
            endOffset="14423"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="14450"
                endOffset="14459"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="322"
            column="72"
            startOffset="14450"
            endLine="322"
            endColumn="80"
            endOffset="14458"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="333"
            column="47"
            startOffset="14952"
            endLine="333"
            endColumn="91"
            endOffset="14996"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="333"
            column="48"
            startOffset="14953"
            endLine="333"
            endColumn="78"
            endOffset="14983"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="335"
            column="47"
            startOffset="15068"
            endLine="335"
            endColumn="80"
            endOffset="15101"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="335"
            column="48"
            startOffset="15069"
            endLine="335"
            endColumn="79"
            endOffset="15100"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="15170"
                endOffset="15179"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="339"
            column="36"
            startOffset="15170"
            endLine="339"
            endColumn="44"
            endOffset="15178"/>
    </incident>

    <incident
        id="KotlinNullnessAnnotation"
        severity="warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`">
        <fix-replace
            description="Delete `@NonNull`"
            robot="true"
            independent="true"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                startOffset="15205"
                endOffset="15214"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="339"
            column="71"
            startOffset="15205"
            endLine="339"
            endColumn="79"
            endOffset="15213"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="340"
            column="43"
            startOffset="15272"
            endLine="340"
            endColumn="76"
            endOffset="15305"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
            line="340"
            column="44"
            startOffset="15273"
            endLine="340"
            endColumn="75"
            endOffset="15304"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="14"
            column="9"
            startOffset="338"
            endLine="14"
            endColumn="23"
            endOffset="352"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-retrofit2-retrofit2"
            robot="true">
            <fix-replace
                description="Replace with squareupRetrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupRetrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="390"
                    endOffset="390"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-retrofit2-retrofit2 = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;squareupRetrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-retrofit2-retrofit2 = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;squareupRetrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="489"
                    endOffset="489"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.retrofit2.retrofit2"
                robot="true"
                replacement="libs.com.squareup.retrofit2.retrofit2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1486"
                    endOffset="1525"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1486"
            endLine="56"
            endColumn="59"
            endOffset="1525"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-retrofit2-converter-gson2"
            robot="true">
            <fix-replace
                description="Replace with squareupConverterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupConverterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="390"
                    endOffset="390"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-retrofit2-converter-gson2 = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;squareupConverterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-retrofit2-converter-gson2 = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;squareupConverterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="489"
                    endOffset="489"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.retrofit2.converter.gson2"
                robot="true"
                replacement="libs.com.squareup.retrofit2.converter.gson2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1546"
                    endOffset="1591"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="57"
            column="20"
            startOffset="1546"
            endLine="57"
            endColumn="65"
            endOffset="1591"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-jjoe64-graphview2"
            robot="true">
            <fix-replace
                description="Replace with jjoe64GraphviewVersion = &quot;4.2.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jjoe64GraphviewVersion = &quot;4.2.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-jjoe64-graphview2 = { module = &quot;com.jjoe64:graphview&quot;, version.ref = &quot;jjoe64GraphviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-jjoe64-graphview2 = { module = &quot;com.jjoe64:graphview&quot;, version.ref = &quot;jjoe64GraphviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="489"
                    endOffset="489"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.jjoe64.graphview2"
                robot="true"
                replacement="libs.com.jjoe64.graphview2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1612"
                    endOffset="1640"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="58"
            column="20"
            startOffset="1612"
            endLine="58"
            endColumn="48"
            endOffset="1640"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-google-code-gson-gson2"
            robot="true">
            <fix-replace
                description="Replace with googleGsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleGsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-google-code-gson-gson2 = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-google-code-gson-gson2 = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;googleGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="489"
                    endOffset="489"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.google.code.gson.gson2"
                robot="true"
                replacement="libs.com.google.code.gson.gson2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1662"
                    endOffset="1696"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="21"
            startOffset="1662"
            endLine="59"
            endColumn="55"
            endOffset="1696"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (com.google.firebase:firebase-bom is already available as `firebase-bom`, but using version 32.1.2 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for com-google-firebase-firebase-bom-v3312-x2"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with comGoogleFirebaseFirebaseBom = &quot;33.1.2&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="comGoogleFirebaseFirebaseBom = &quot;33.1.2&quot;&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="27"
                        endOffset="27"/>
                </fix-replace>
                <fix-replace
                    description="Replace with com-google-firebase-firebase-bom-v3312-x2 = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;comGoogleFirebaseFirebaseBom&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="com-google-firebase-firebase-bom-v3312-x2 = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;comGoogleFirebaseFirebaseBom&quot; }&#xA;"
                    priority="0">
                    <range
                        file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                        startOffset="489"
                        endOffset="489"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.com.google.firebase.firebase.bom.v3312.x2"
                    robot="true"
                    independent="true"
                    replacement="libs.com.google.firebase.firebase.bom.v3312.x2"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle.kts"
                        startOffset="1717"
                        endOffset="1758"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `firebase-bom` (version 32.1.2)"
                replacement="libs.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1717"
                    endOffset="1758"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="20"
            startOffset="1717"
            endLine="60"
            endColumn="61"
            endOffset="1758"/>
    </incident>

    <incident
        id="BomWithoutPlatform"
        severity="warning"
        message="BOM should be added with a call to platform()">
        <fix-replace
            description="Add platform() to BOM declaration"
            family="Add platform() to BOM declaration"
            oldString="&quot;com.google.firebase:firebase-bom:33.1.2&quot;"
            replacement="platform(&quot;com.google.firebase:firebase-bom:33.1.2&quot;)"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="20"
            startOffset="1717"
            endLine="60"
            endColumn="61"
            endOffset="1758"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-work-work-runtime2"
            robot="true">
            <fix-replace
                description="Replace with androidxWorkRuntimeVersion = &quot;2.8.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxWorkRuntimeVersion = &quot;2.8.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="27"
                    endOffset="27"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-work-work-runtime2 = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;androidxWorkRuntimeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-work-work-runtime2 = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;androidxWorkRuntimeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
                    startOffset="405"
                    endOffset="405"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.work.work.runtime2"
                robot="true"
                replacement="libs.androidx.work.work.runtime2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1780"
                    endOffset="1814"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="21"
            startOffset="1780"
            endLine="61"
            endColumn="55"
            endOffset="1814"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="107"
            endLine="6"
            endColumn="23"
            endOffset="114"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="131"
            endLine="7"
            endColumn="23"
            endOffset="138"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="152"
            endLine="8"
            endColumn="20"
            endOffset="159"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="172"
            endLine="9"
            endColumn="20"
            endOffset="180"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="10"
            column="20"
            startOffset="201"
            endLine="10"
            endColumn="27"
            endOffset="208"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="12"
            column="22"
            startOffset="257"
            endLine="12"
            endColumn="29"
            endOffset="264"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="13"
            column="16"
            startOffset="281"
            endLine="13"
            endColumn="23"
            endOffset="288"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="32.1.2"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="14"
            column="15"
            startOffset="304"
            endLine="14"
            endColumn="23"
            endOffset="312"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1">
        <fix-replace
            description="Change to 23.2.1"
            family="Update versions"
            oldString="23.0.0"
            replacement="23.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="16"
            column="16"
            startOffset="356"
            endLine="16"
            endColumn="24"
            endOffset="364"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.5.1"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.5.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.5.1"
                replacement="8.5.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="14"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="33.1.2"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="20"
            startOffset="1717"
            endLine="60"
            endColumn="61"
            endOffset="1758"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.8.0"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="21"
            startOffset="1780"
            endLine="61"
            endColumn="55"
            endOffset="1814"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logos.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/logos.png"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icon used as round icon did not have a circular shape">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/logos.png"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addexpense.xml"
            line="8"
            column="5"
            startOffset="314"
            endLine="8"
            endColumn="46"
            endOffset="355"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/addtransaction.xml"
            line="7"
            column="5"
            startOffset="295"
            endLine="7"
            endColumn="46"
            endOffset="336"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/base.xml"
            line="7"
            column="5"
            startOffset="296"
            endLine="7"
            endColumn="38"
            endOffset="329"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dashboard.xml"
            line="8"
            column="5"
            startOffset="314"
            endLine="8"
            endColumn="46"
            endOffset="355"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/language.xml"
            line="5"
            column="5"
            startOffset="203"
            endLine="5"
            endColumn="38"
            endOffset="236"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base_Theme_BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/login.xml"
            line="7"
            column="5"
            startOffset="312"
            endLine="7"
            endColumn="46"
            endOffset="353"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notifications.xml"
            line="7"
            column="5"
            startOffset="347"
            endLine="7"
            endColumn="46"
            endOffset="388"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/register.xml"
            line="7"
            column="8"
            startOffset="330"
            endLine="7"
            endColumn="49"
            endOffset="371"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/settings.xml"
            line="16"
            column="5"
            startOffset="794"
            endLine="16"
            endColumn="46"
            endOffset="835"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;budget_info&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2910"
            endLine="55"
            endColumn="31"
            endOffset="2928"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;zulu&quot; is not translated in &quot;af&quot; (Afrikaans) or &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="3497"
            endLine="66"
            endColumn="24"
            endOffset="3508"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;name_bug&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="3536"
            endLine="67"
            endColumn="28"
            endOffset="3551"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;bug_amount&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="3579"
            endLine="68"
            endColumn="30"
            endOffset="3596"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;exp_amount&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="3626"
            endLine="69"
            endColumn="30"
            endOffset="3643"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;ext_name&quot; is not translated in &quot;af&quot; (Afrikaans) or &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="3673"
            endLine="70"
            endColumn="28"
            endOffset="3688"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;exp_date&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="3716"
            endLine="71"
            endColumn="28"
            endOffset="3731"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;date_x&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3763"
            endLine="72"
            endColumn="26"
            endOffset="3776"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;main_cash&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="3804"
            endLine="73"
            endColumn="29"
            endOffset="3820"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;_0&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="3853"
            endLine="74"
            endColumn="22"
            endOffset="3862"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;month&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="3888"
            endLine="75"
            endColumn="25"
            endOffset="3900"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;pick_date&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="76"
            column="13"
            startOffset="3934"
            endLine="76"
            endColumn="29"
            endOffset="3950"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;budget&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="3983"
            endLine="77"
            endColumn="26"
            endOffset="3996"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;target&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="4026"
            endLine="78"
            endColumn="26"
            endOffset="4039"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;balance&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="4069"
            endLine="79"
            endColumn="27"
            endOffset="4083"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;set_new_target&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4114"
            endLine="80"
            endColumn="34"
            endOffset="4135"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;set&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="81"
            column="13"
            startOffset="4171"
            endLine="81"
            endColumn="23"
            endOffset="4181"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;sign_up&quot; is not translated in &quot;af&quot; (Afrikaans), &quot;en&quot; (English), &quot;zu&quot; (Zulu)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="82"
            column="13"
            startOffset="4208"
            endLine="82"
            endColumn="27"
            endOffset="4222"/>
    </incident>

</incidents>
