<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="addtransaction" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\addtransaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/addtransaction_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="159" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="150" startOffset="4" endLine="158" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="156" startOffset="8" endLine="157" endOffset="38"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="8" startOffset="4" endLine="16" endOffset="33"/></Target><Target id="@+id/title" view="TextView"><Expressions/><location startLine="18" startOffset="4" endLine="31" endOffset="34"/></Target><Target id="@+id/linearLayout3" view="LinearLayout"><Expressions/><location startLine="33" startOffset="4" endLine="40" endOffset="42"/></Target><Target id="@+id/linearLayout" view="LinearLayout"><Expressions/><location startLine="42" startOffset="4" endLine="60" endOffset="18"/></Target><Target id="@+id/budget_info" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="59" endOffset="37"/></Target><Target id="@+id/linearLayout5" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="97" endOffset="18"/></Target><Target id="@+id/itemNameHeader" view="TextView"><Expressions/><location startLine="70" startOffset="8" endLine="78" endOffset="38"/></Target><Target id="@+id/itemAmountHeader" view="TextView"><Expressions/><location startLine="80" startOffset="8" endLine="87" endOffset="38"/></Target><Target id="@+id/itemDateHeader" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="96" endOffset="38"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="99" startOffset="4" endLine="104" endOffset="41"/></Target><Target id="@+id/editText5" view="EditText"><Expressions/><location startLine="106" startOffset="4" endLine="118" endOffset="33"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="120" startOffset="4" endLine="133" endOffset="33"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="135" startOffset="4" endLine="148" endOffset="34"/></Target></Targets></Layout>