<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.5.1">

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                        ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="120"
            column="41"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="120"
            column="76"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="136"
            column="40"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                          ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="136"
            column="75"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="311"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="311"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="330"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="330"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    private fun getExpenses(@NonNull response: Response&lt;List&lt;Record>>): List&lt;Expenses>? {"
        errorLine2="                            ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\DashboardActivity.kt"
            line="338"
            column="29"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                            ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="128"
            column="45"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="128"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="142"
            column="44"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="142"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="204"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="204"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                        ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="219"
            column="57"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="219"
            column="84"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="227"
            column="56"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                                                  ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="227"
            column="83"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="244"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="244"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    @NonNull"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt"
            line="12"
            column="5"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewGroup` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt"
            line="13"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {"
        errorLine2="                                  ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\Item2Adapter.kt"
            line="19"
            column="35"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    @NonNull"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt"
            line="12"
            column="5"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewGroup` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt"
            line="13"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `ViewHolder` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {"
        errorLine2="                                  ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ItemAdapter.kt"
            line="19"
            column="35"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="168"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="168"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="186"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="186"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="217"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="217"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="225"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="225"
            column="63"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `CharSequence` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onAuthenticationError(errorCode: Int, @NonNull errString: CharSequence) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="234"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `BiometricPrompt.AuthenticationResult` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onAuthenticationSucceeded(@NonNull result: BiometricPrompt.AuthenticationResult) {"
        errorLine2="                                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\MainActivity.kt"
            line="239"
            column="52"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt"
            line="84"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt"
            line="84"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt"
            line="116"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\NotificationActivity.kt"
            line="116"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt"
            line="177"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt"
            line="177"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt"
            line="186"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\RegisterActivity.kt"
            line="186"
            column="63"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                            ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="167"
            column="45"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="167"
            column="80"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="195"
            column="44"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                    override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="195"
            column="79"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `KeyEvent` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    override fun onKeyDown(keyCode: Int, @NonNull event: KeyEvent): Boolean {"
        errorLine2="                                         ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="209"
            column="42"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="271"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="271"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="299"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="299"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="353"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="353"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="365"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="365"
            column="63"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="382"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="382"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="392"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="392"
            column="63"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Context` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    @NonNull context: Context,"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="17"
            column="5"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `WorkerParameters` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    @NonNull params: WorkerParameters"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="18"
            column="5"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Result` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="    @NonNull"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="24"
            column="5"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="98"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="98"
            column="64"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="106"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SyncWorker.kt"
            line="106"
            column="63"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="141"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="141"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                        override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="162"
            column="49"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                        override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                                           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="162"
            column="76"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                        override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="176"
            column="48"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                        override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                                          ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="176"
            column="75"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="188"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="188"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="214"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="214"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                        ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="228"
            column="57"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onResponse(@NonNull call: Call&lt;Void>, @NonNull response: Response&lt;Void>) {"
        errorLine2="                                                                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="228"
            column="84"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;Void>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="236"
            column="56"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="                                override fun onFailure(@NonNull call: Call&lt;Void>, @NonNull t: Throwable) {"
        errorLine2="                                                                                  ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="236"
            column="83"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="248"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="248"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="287"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="287"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="307"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="307"
            column="71"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="322"
            column="37"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Response&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onResponse(@NonNull call: Call&lt;List&lt;Record>>, @NonNull response: Response&lt;List&lt;Record>>) {"
        errorLine2="                                                                       ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="322"
            column="72"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Call&lt;List&lt;Record>>` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                   ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="339"
            column="36"/>
    </issue>

    <issue
        id="KotlinNullnessAnnotation"
        severity="Warning"
        message="Do not use `@NonNull` in Kotlin; the nullability is already implied by the Kotlin type `Throwable` **not** ending with `?`"
        category="Correctness"
        priority="6"
        summary="Kotlin nullability annotation"
        explanation="In Kotlin, nullness is part of the type system; `s: String` is **never** null and `s: String?` is sometimes null, whether or not you add in additional annotations stating `@NonNull` or `@Nullable`. These are likely copy/paste mistakes, and are misleading."
        errorLine1="            override fun onFailure(@NonNull call: Call&lt;List&lt;Record>>, @NonNull t: Throwable) {"
        errorLine2="                                                                      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="339"
            column="71"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk = 34"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="AppBundleLocaleChanges"
        severity="Warning"
        message="Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the `bundle` configuration"
        category="Correctness"
        priority="5"
        summary="App Bundle handling of runtime locale changes"
        explanation="When changing locales at runtime (e.g. to provide an in-app language switcher), the Android App Bundle must be configured to not split by locale or the Play Core library must be used to download additional locales at runtime."
        url="https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes"
        urls="https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes"
        errorLine1="        config.setLocale(locale)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\LanguageActivity.kt"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.5.1&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.5.1&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.5.1&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="BomWithoutPlatform"
        severity="Warning"
        message="BOM should be added with a call to platform()"
        category="Correctness"
        priority="4"
        summary="Using a BOM without platform call"
        explanation="When including a BOM, the dependency&apos;s coordinates must be wrapped in a call to `platform()` for Gradle to interpret it correctly."
        url="https://developer.android.com/r/tools/gradle-bom-docs"
        urls="https://developer.android.com/r/tools/gradle-bom-docs"
        errorLine1="    implementation(&quot;com.google.firebase:firebase-bom:33.1.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="64"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation(&quot;com.google.firebase:firebase-bom:33.1.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="64"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation (&quot;androidx.work:work-runtime:2.8.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="material = &quot;1.10.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="material = &quot;1.10.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="material = &quot;1.10.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="10"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="10"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="10"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationFragment = &quot;2.6.0&quot;"
        errorLine2="                     ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="12"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationFragment = &quot;2.6.0&quot;"
        errorLine2="                     ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="12"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationFragment = &quot;2.6.0&quot;"
        errorLine2="                     ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="12"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationUi = &quot;2.6.0&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="13"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationUi = &quot;2.6.0&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="13"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="navigationUi = &quot;2.6.0&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="13"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseBom = &quot;32.1.2&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="14"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseBom = &quot;32.1.2&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="14"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseBom = &quot;32.1.2&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="14"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseAuth = &quot;23.0.0&quot;"
        errorLine2="               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="16"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseAuth = &quot;23.0.0&quot;"
        errorLine2="               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="16"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="firebaseAuth = &quot;23.0.0&quot;"
        errorLine2="               ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\gradle\libs.versions.toml"
            line="16"
            column="16"/>
    </issue>

    <issue
        id="UnsafeIntentLaunch"
        severity="Warning"
        message="This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.ExpensesActivity. You could either make the component com.example.budgettracker.ExpensesActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer."
        category="Security"
        priority="6"
        summary="Launched Unsafe Intent"
        explanation="Intent that potentially could come from an untrusted source should not be launched from an unprotected component without first being sanitized. See this support FAQ for details: https://support.google.com/faqs/answer/9267555"
        errorLine1="            val intent = getIntent()"
        errorLine2="                         ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="173"
            column="26"/>
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\ExpensesActivity.kt"
            line="175"
            column="13"
            message="The unsafe intent is launched here."/>
    </issue>

    <issue
        id="UnsafeIntentLaunch"
        severity="Warning"
        message="This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.TransactionsActivity. You could either make the component com.example.budgettracker.TransactionsActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer."
        category="Security"
        priority="6"
        summary="Launched Unsafe Intent"
        explanation="Intent that potentially could come from an untrusted source should not be launched from an unprotected component without first being sanitized. See this support FAQ for details: https://support.google.com/faqs/answer/9267555"
        errorLine1="            val intent = getIntent()"
        errorLine2="                         ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="123"
            column="26"/>
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="125"
            column="13"
            message="The unsafe intent is launched here."/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="               android:layout_below=&quot;@id/description&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="64"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_below`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="               android:layout_below=&quot;@id/description&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="87"
            column="16"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      android:pathData=&quot;M644,532L586,474Q595,427 559,386Q523,345 466,354L408,296Q425,288 442.5,284Q460,280 480,280Q555,280 607.5,332.5Q660,385 660,460Q660,480 656,497.5Q652,515 644,532ZM772,658L714,602Q752,573 781.5,538.5Q811,504 832,460Q782,359 688.5,299.5Q595,240 480,240Q451,240 423,244Q395,248 368,256L306,194Q347,177 390,168.5Q433,160 480,160Q631,160 749,243.5Q867,327 920,460Q897,519 859.5,569.5Q822,620 772,658ZM792,904L624,738Q589,749 553.5,754.5Q518,760 480,760Q329,760 211,676.5Q93,593 40,460Q61,407 93,361.5Q125,316 166,280L56,168L112,112L848,848L792,904ZM222,336Q193,362 169,393Q145,424 128,460Q178,561 271.5,620.5Q365,680 480,680Q500,680 519,677.5Q538,675 558,672L522,634Q511,637 501,638.5Q491,640 480,640Q405,640 352.5,587.5Q300,535 300,460Q300,449 301.5,439Q303,429 306,418L222,336ZM541,429L541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429ZM390,504Q390,504 390,504Q390,504 390,504L390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\eye_close.xml"
            line="9"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      android:pathData=&quot;M481,179Q587,179 681,224.5Q775,270 838,356Q845,365 842.5,372Q840,379 834,384Q828,389 820,388.5Q812,388 806,380Q751,302 664.5,260.5Q578,219 481,219Q384,219 299,260.5Q214,302 158,380Q152,389 144,390Q136,391 130,386Q123,381 121.5,373.5Q120,366 126,358Q188,273 281.5,226Q375,179 481,179ZM481,273Q616,273 713,363Q810,453 810,586Q810,636 774.5,669.5Q739,703 688,703Q637,703 600.5,669.5Q564,636 564,586Q564,553 539.5,530.5Q515,508 481,508Q447,508 422.5,530.5Q398,553 398,586Q398,683 455.5,748Q513,813 604,839Q613,842 616,849Q619,856 617,864Q615,871 609,876Q603,881 594,879Q490,853 424,775.5Q358,698 358,586Q358,536 394,502Q430,468 481,468Q532,468 568,502Q604,536 604,586Q604,619 629,641.5Q654,664 688,664Q722,664 746,641.5Q770,619 770,586Q770,470 685,391Q600,312 482,312Q364,312 279,391Q194,470 194,585Q194,609 198.5,645Q203,681 220,729Q223,738 219.5,745Q216,752 208,755Q200,758 192.5,754.5Q185,751 182,743Q167,704 160.5,665.5Q154,627 154,586Q154,453 250.5,363Q347,273 481,273ZM481,81Q545,81 606,96.5Q667,112 724,141Q733,146 734.5,153Q736,160 733,167Q730,174 723,178Q716,182 706,177Q653,150 596.5,135.5Q540,121 481,121Q423,121 367,134.5Q311,148 260,177Q252,182 244,179.5Q236,177 232,169Q228,161 230,154.5Q232,148 240,143Q296,113 357,97Q418,81 481,81ZM481,370Q574,370 641,432.5Q708,495 708,586Q708,595 702.5,600.5Q697,606 688,606Q680,606 674,600.5Q668,595 668,586Q668,511 612.5,460.5Q557,410 481,410Q405,410 350.5,460.5Q296,511 296,586Q296,667 324,723.5Q352,780 406,837Q412,843 412,851Q412,859 406,865Q400,871 392,871Q384,871 378,865Q319,803 287.5,738.5Q256,674 256,586Q256,495 322,432.5Q388,370 481,370ZM480,566Q489,566 494.5,572Q500,578 500,586Q500,661 554,709Q608,757 680,757Q686,757 697,756Q708,755 720,753Q729,751 735.5,755.5Q742,760 744,769Q746,777 741,783Q736,789 728,791Q710,796 696.5,796.5Q683,797 680,797Q591,797 525.5,737Q460,677 460,586Q460,578 465.5,572Q471,566 480,566Z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\fingerprint.xml"
            line="9"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      android:pathData=&quot;M480,480Q414,480 367,433Q320,386 320,320Q320,254 367,207Q414,160 480,160Q546,160 593,207Q640,254 640,320Q640,386 593,433Q546,480 480,480ZM160,800L160,688Q160,654 177.5,625.5Q195,597 224,582Q286,551 350,535.5Q414,520 480,520Q546,520 610,535.5Q674,551 736,582Q765,597 782.5,625.5Q800,654 800,688L800,800L160,800ZM240,720L720,720L720,688Q720,677 714.5,668Q709,659 700,654Q646,627 591,613.5Q536,600 480,600Q424,600 369,613.5Q314,627 260,654Q251,659 245.5,668Q240,677 240,688L240,720ZM480,400Q513,400 536.5,376.5Q560,353 560,320Q560,287 536.5,263.5Q513,240 480,240Q447,240 423.5,263.5Q400,287 400,320Q400,353 423.5,376.5Q447,400 480,400ZM480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320ZM480,720L480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720L480,720Z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\person.xml"
            line="9"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      android:pathData=&quot;M370,880L354,752Q341,747 329.5,740Q318,733 307,725L188,775L78,585L181,507Q180,500 180,493.5Q180,487 180,480Q180,473 180,466.5Q180,460 181,453L78,375L188,185L307,235Q318,227 330,220Q342,213 354,208L370,80L590,80L606,208Q619,213 630.5,220Q642,227 653,235L772,185L882,375L779,453Q780,460 780,466.5Q780,473 780,480Q780,487 780,493.5Q780,500 778,507L881,585L771,775L653,725Q642,733 630,740Q618,747 606,752L590,880L370,880ZM440,800L519,800L533,694Q564,686 590.5,670.5Q617,655 639,633L738,674L777,606L691,541Q696,527 698,511.5Q700,496 700,480Q700,464 698,448.5Q696,433 691,419L777,354L738,286L639,328Q617,305 590.5,289.5Q564,274 533,266L520,160L441,160L427,266Q396,274 369.5,289.5Q343,305 321,327L222,286L183,354L269,418Q264,433 262,448Q260,463 260,480Q260,496 262,511Q264,526 269,541L183,606L222,674L321,632Q343,655 369.5,670.5Q396,686 427,694L440,800ZM482,620Q540,620 581,579Q622,538 622,480Q622,422 581,381Q540,340 482,340Q423,340 382.5,381Q342,422 342,480Q342,538 382.5,579Q423,620 482,620ZM480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480Z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\settings_24px.xml"
            line="9"
            column="25"/>
    </issue>

    <issue
        id="InefficientWeight"
        severity="Warning"
        message="Use a `layout_width` of `0dp` instead of `185dp` for better performance"
        category="Performance"
        priority="3"
        summary="Inefficient layout weight"
        explanation="When only a single widget in a `LinearLayout` defines a weight, it is more efficient to assign a width/height of `0dp` to it since it will absorb all the remaining space anyway. With a declared width/height of `0dp` it does not have to measure its own size first."
        errorLine1="        android:layout_width=&quot;185dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml"
            line="237"
            column="9"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\base.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\dashboard.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\language.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base_Theme_BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\login.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\notifications.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="       android:background=&quot;@android:color/white&quot;"
        errorLine2="       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="7"
            column="8"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Base.Theme.BudgetTracker`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@android:color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml"
            line="16"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_main` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\activity_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.button_ripple` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\button_ripple.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.onPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;onPrimary&quot;>#ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.border_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;border_color&quot;>#dae1e7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.back_ground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;back_ground&quot;>#b0463a&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.content_main` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;androidx.constraintlayout.widget.ConstraintLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\content_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.fab_margin` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;fab_margin&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\dimens.xml"
            line="2"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.mipmap.ic_launcher` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher.webp"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.mipmap.ic_launcher_round` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.mail_24px` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\mail_24px.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.menu_main` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\menu\menu_main.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.monitoring` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\monitoring.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.navigation.nav_graph` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;navigation xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\navigation\nav_graph.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.search` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\search.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.settings_24px` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\settings_24px.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.spinner_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_background.xml"
            line="17"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.spinner_item` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\spinner_item.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.spinner_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_normal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.spinner_press` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_press.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.spinner_select` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\spinner_select.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.action_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;action_settings&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.first_fragment_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;first_fragment_label&quot;>First Fragment&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.second_fragment_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;second_fragment_label&quot;>Second Fragment&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.next` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;next&quot;>Next&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.previous` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;previous&quot;>Previous&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.search_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;search_description&quot;>Search&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.chart_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;chart_description&quot;>Chart&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.cashmate` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;cashmate&quot;>CashMate&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.today` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;today&quot;>Today&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.select_category` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;select_category&quot;>Select category&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string._5_943` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;_5_943&quot;>$5,943&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.last_30_days` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;last_30_days&quot;>This month&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.meta` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;meta&quot;>META&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.meta_platforms` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;meta_platforms&quot;>Meta Platforms&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.budget_info` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;budget_info&quot;>Your budget for this month is $%d&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.user_profile` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;user_profile&quot;>user profile&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.main_cash` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;main_cash&quot;>Main_cash&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.array.categories_array` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string-array name=&quot;categories_array&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\strings.xml"
            line="83"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.AppTheme` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;AppTheme&quot; parent=&quot;Theme.MaterialComponents.Light.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\values\styles.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.work_24px` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\work_24px.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-hdpi\logos.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-mdpi\logos.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xhdpi\logos.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xxhdpi\logos.png"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icon used as round icon did not have a circular shape"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\mipmap-xxxhdpi\logos.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/logos.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\drawable\logos.png"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml"
            line="53"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml"
            line="66"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="107"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="121"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\login.xml"
            line="48"
            column="10"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="71"
            column="17"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="94"
            column="17"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml"
            line="186"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml"
            line="53"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addexpense.xml"
            line="66"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="107"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="121"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="71"
            column="17"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="94"
            column="17"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="117"
            column="17"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="               &lt;EditText"
        errorLine2="                ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="150"
            column="17"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\settings.xml"
            line="186"
            column="14"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.retrofit2:retrofit:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="60"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.retrofit2:converter-gson:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="61"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.jjoe64:graphview:4.2.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="62"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation (&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="63"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (com.google.firebase:firebase-bom is already available as `firebase-bom`, but using version 32.1.2 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.google.firebase:firebase-bom:33.1.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="64"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation (&quot;androidx.work:work-runtime:2.8.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\build.gradle.kts"
            line="65"
            column="21"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\list_bug.xml"
            line="8"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\list_item.xml"
            line="7"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="               &lt;ImageView"
        errorLine2="                ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="130"
            column="17"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="               &lt;ImageView"
        errorLine2="                ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="163"
            column="17"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                balanceTextView.text = &quot;0.00&quot;"
        errorLine2="                                       ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="143"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                balanceTextView.text = &quot;0.00&quot;"
        errorLine2="                                        ~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="143"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                targetTextView.text = &quot;0.00&quot;"
        errorLine2="                                      ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="144"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                targetTextView.text = &quot;0.00&quot;"
        errorLine2="                                       ~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="144"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                budgetTextView.text = &quot;0.00&quot;"
        errorLine2="                                      ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="145"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                budgetTextView.text = &quot;0.00&quot;"
        errorLine2="                                       ~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\SettingsActivity.kt"
            line="145"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    budgetInfoTextView.text = &quot;Total Budget for this month: R$totalBudget&quot;"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="333"
            column="47"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    budgetInfoTextView.text = &quot;Total Budget for this month: R$totalBudget&quot;"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="333"
            column="48"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    budgetInfoTextView.text = &quot;Total Budget for this month: R0&quot;"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="335"
            column="47"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    budgetInfoTextView.text = &quot;Total Budget for this month: R0&quot;"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="335"
            column="48"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                budgetInfoTextView.text = &quot;Total Budget for this month: R0&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="340"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                budgetInfoTextView.text = &quot;Total Budget for this month: R0&quot;"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\kotlin\com\example\budgettracker\TransactionsActivity.kt"
            line="340"
            column="44"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Your budget for this month is $0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Your budget for this month is $0&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\addtransaction.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Create Account&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Create Account&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\register.xml"
            line="42"
            column="17"/>
    </issue>

    <issue
        id="RelativeOverlap"
        severity="Warning"
        message="`@id/button` can overlap `@id/back_button` if @id/button grows due to localized text expansion"
        category="Internationalization"
        priority="3"
        summary="Overlapping items in RelativeLayout"
        explanation="If relative layout has text or button items aligned to left and right sides they can overlap each other due to localized text expansion unless they have mutual constraints like `toEndOf`/`toStartOf`."
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\BudgetTracker\app\src\main\res\layout\notifications.xml"
            line="49"
            column="10"/>
    </issue>

</issues>
