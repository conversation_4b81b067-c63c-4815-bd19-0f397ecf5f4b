package com.example.budgettracker

import android.content.ContentValues
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.os.Handler
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Log
import android.view.View
import android.widget.*
import androidx.annotation.NonNull
import androidx.appcompat.app.AppCompatActivity
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.Executor

class MainActivity : AppCompatActivity() {

    private lateinit var usernameEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var mAuth: FirebaseAuth
    private lateinit var sessionManager: SessionManager
    private lateinit var dbHelper: DatabaseHelper
    private var isPasswordVisible = false
    private lateinit var loadingSpinner: LoadingSpinner
    private var displayname2: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        if (NetworkUtils.isInternetAvailable(this)) {
            Toast.makeText(this@MainActivity, "Online", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this@MainActivity, "Offline", Toast.LENGTH_SHORT).show()
        }

        setContentView(R.layout.login)
        FirebaseApp.initializeApp(this)
        dbHelper = DatabaseHelper(this)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        mAuth = FirebaseAuth.getInstance()
        sessionManager = SessionManager(this)

        usernameEditText = findViewById(R.id.email_input)
        passwordEditText = findViewById(R.id.password_input)
        val loginButton = findViewById<Button>(R.id.login_button)
        val fingerprintButton = findViewById<ImageView>(R.id.fingerprint_icon)
        val passwordEyeImageView = findViewById<ImageView>(R.id.password_eye)
        
        loginButton.setOnClickListener {
            loadingSpinner.show()
            val username = usernameEditText.text.toString()
            val password = passwordEditText.text.toString()
            if (username.isNotEmpty() && password.isNotEmpty()) {
                performLogin(username, password)
            } else {
                Toast.makeText(this@MainActivity, "Please enter username and password", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }
        }
        
        val figText1 = findViewById<TextView>(R.id.fingerprint_instead)
        val figText2 = findViewById<TextView>(R.id.or_login_with)

        if (sessionManager.isLoggedIn()) {
            fingerprintButton.visibility = View.VISIBLE
            figText1.visibility = View.VISIBLE
            figText2.visibility = View.VISIBLE
        } else {
            fingerprintButton.visibility = View.GONE
            figText1.visibility = View.GONE
            figText2.visibility = View.GONE
        }

        fingerprintButton.setOnClickListener { startFingerprintAuthentication() }
        
        val signUpInsteadTextView = findViewById<TextView>(R.id.signup_instead)
        signUpInsteadTextView.setOnClickListener {
            val intent = Intent(this@MainActivity, RegisterActivity::class.java)
            startActivity(intent)
        }

        passwordEyeImageView.setOnClickListener { togglePasswordVisibility() }
    }

    private fun togglePasswordVisibility() {
        val passwordEyeImageView = findViewById<ImageView>(R.id.password_eye)
        if (isPasswordVisible) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            passwordEyeImageView.setImageResource(R.drawable.eye_close)
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            passwordEyeImageView.setImageResource(R.drawable.eye_icon)
        }
        isPasswordVisible = !isPasswordVisible
        passwordEditText.setSelection(passwordEditText.text.length)
    }

    private fun performLogin(email: String, password: String) {
        mAuth.signInWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    val user = mAuth.currentUser
                    if (user != null) {
                        if (NetworkUtils.isInternetAvailable(this)) {
                            getUserDisplayNameOnline(user.uid) { displayName ->
                                displayname2 = displayName ?: getUserDisplayName(user.uid)
                                sessionManager.createLoginSession(email, displayname2!!, user.uid)
                                proceedToDashboard()
                            }
                        } else {
                            displayname2 = getUserDisplayName(user.uid)
                            sessionManager.createLoginSession(email, displayname2!!, user.uid)
                            proceedToDashboard()
                        }
                    }
                } else {
                    Toast.makeText(this@MainActivity, "Authentication failed.", Toast.LENGTH_SHORT).show()
                    loadingSpinner.hide()
                }
            }
    }

    private fun proceedToDashboard() {
        Handler().postDelayed({
            loadingSpinner.hide()
            val intent = Intent(this@MainActivity, DashboardActivity::class.java)
            startActivity(intent)
            finish()
        }, 2000)
    }

    private fun getUserDisplayName(userId: String): String {
        val db = dbHelper.readableDatabase
        val cursor = db.rawQuery("SELECT user_name FROM user WHERE user_id = ?", arrayOf(userId))
        var displayName = "Unknown User"
        
        if (cursor.moveToFirst()) {
            displayName = cursor.getString(0)
        }
        cursor.close()
        db.close()
        return displayName
    }

    private fun getUserDisplayNameOnline(userId: String, callback: DisplayNameCallback) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val sdf = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val call = apiService.getRecords("user", currentDate, userId)

        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    if (records.isNotEmpty()) {
                        val user = records[0] as User
                        displayname2 = user.getUserName()
                        callback.onDisplayNameRetrieved(displayname2)
                        Log.w("MainActivity", "User ID: ${user.getuser_id()}, User Name: $displayname2")
                    } else {
                        Log.w("MainActivity", "No user found")
                        callback.onDisplayNameRetrieved(null)
                    }
                } else {
                    Log.w("MainActivity", "Failed to fetch username")
                    callback.onDisplayNameRetrieved(null)
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Log.w("MainActivity", "Network error: ${t.message}")
                callback.onDisplayNameRetrieved(null)
            }
        })
    }

    interface DisplayNameCallback {
        fun onDisplayNameRetrieved(displayName: String?)
    }

    private fun saveUserToDatabase(user: FirebaseUser) {
        val db = dbHelper.writableDatabase
        val userId = user.uid
        val values = ContentValues().apply {
            put("user_status", 2)
        }
        val selection = "user_id = ?"
        val selectionArgs = arrayOf(userId)
        db.update("user", values, selection, selectionArgs)
        db.close()
    }

    private fun saveUserOnline(user: FirebaseUser) {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val userId = user.uid
        val updatedValues = hashMapOf<String, Any>("user_status" to 2)
        val call = apiService.updateRecord("user", userId, dateFormat.toString(), updatedValues)
        
        call.enqueue(object : Callback<Void> {
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                if (response.isSuccessful) {
                    Log.d("SaveUser", "User status updated successfully.")
                } else {
                    Log.e("SaveUser", "Error updating user status: ${response.message()}")
                }
            }

            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                Log.e("SaveUser", "Network error: ${t.message}")
            }
        })
    }

    private fun startFingerprintAuthentication() {
        val executor: Executor = ContextCompat.getMainExecutor(this)
        val biometricPrompt = BiometricPrompt(this@MainActivity, executor, object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(errorCode: Int, @NonNull errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                Toast.makeText(this@MainActivity, "Authentication error: $errString", Toast.LENGTH_SHORT).show()
            }

            override fun onAuthenticationSucceeded(@NonNull result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                Toast.makeText(this@MainActivity, "Authentication succeeded!", Toast.LENGTH_SHORT).show()
                val intent = Intent(this@MainActivity, DashboardActivity::class.java)
                startActivity(intent)
                finish()
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                Toast.makeText(this@MainActivity, "Authentication failed", Toast.LENGTH_SHORT).show()
            }
        })

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle("Fingerprint Authentication")
            .setSubtitle("Log in using your fingerprint")
            .setNegativeButtonText("Cancel")
            .build()

        biometricPrompt.authenticate(promptInfo)
    }
}
