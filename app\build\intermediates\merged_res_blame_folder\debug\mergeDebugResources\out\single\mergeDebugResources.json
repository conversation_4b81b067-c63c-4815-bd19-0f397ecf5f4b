[{"merged": "com.example.budgettracker.app-debug-59:/xml_backup_rules.xml.flat", "source": "com.example.budgettracker.app-main-61:/xml/backup_rules.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_dashboard.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/dashboard.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_button_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/button_background.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_login.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/login.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_eye_icon.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/eye_icon.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_home.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/home.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_monitoring.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/monitoring.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_notifications.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/notifications.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_addtransaction.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/addtransaction.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxxhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_mail_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/mail_24px.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_spinner_press.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_press.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_ic_launcher_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/menu_menu_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/menu/menu_main.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/navigation_nav_graph.xml.flat", "source": "com.example.budgettracker.app-main-61:/navigation/nav_graph.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_spinner_item.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/spinner_item.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_edittext_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/edittext_background.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_base.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/base.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_credit_card.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/credit_card.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_fingerprint.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/fingerprint.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/xml_data_extraction_rules.xml.flat", "source": "com.example.budgettracker.app-main-61:/xml/data_extraction_rules.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_spinner_select.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_select.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_eye_close.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/eye_close.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_person.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/person.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_back.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/back.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_work_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/work_24px.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_content_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/content_main.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_list_item.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/list_item.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_backg_button.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/backg_button.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-mdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_shopping.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/shopping.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_attach_money.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/attach_money.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_button_ripple.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/button_ripple.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_warning_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/warning_24px.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_search.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/search.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_spinner_background.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_background.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_spinner.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/spinner.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_spinner_normal.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/spinner_normal.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_settings_24px.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/settings_24px.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_addexpense.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/addexpense.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_register.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/register.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_notifications.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/notifications.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/drawable/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xhdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xhdpi/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-hdpi_logos.png.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-hdpi/logos.png"}, {"merged": "com.example.budgettracker.app-debug-59:/drawable_logout.xml.flat", "source": "com.example.budgettracker.app-main-61:/drawable/logout.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_list_bug.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/list_bug.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_language.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/language.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.budgettracker.app-main-61:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_settings.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/settings.xml"}, {"merged": "com.example.budgettracker.app-debug-59:/layout_activity_main.xml.flat", "source": "com.example.budgettracker.app-main-61:/layout/activity_main.xml"}]