<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="addexpense" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\addexpense.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/addexpense_0" view="RelativeLayout"><Expressions/><location startLine="2" startOffset="0" endLine="138" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="128" startOffset="4" endLine="136" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="134" startOffset="8" endLine="135" endOffset="38"/></Target><Target id="@+id/editText3" view="TextView"><Expressions/><location startLine="11" startOffset="4" endLine="25" endOffset="43"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="37" endOffset="33"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="39" startOffset="4" endLine="50" endOffset="33"/></Target><Target id="@+id/editText2" view="EditText"><Expressions/><location startLine="52" startOffset="4" endLine="63" endOffset="34"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="65" startOffset="4" endLine="78" endOffset="34"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="80" startOffset="4" endLine="94" endOffset="34"/></Target><Target id="@+id/category_spinner" view="Spinner"><Expressions/><location startLine="96" startOffset="4" endLine="110" endOffset="34"/></Target><Target id="@+id/date_picker_button" view="Button"><Expressions/><location startLine="112" startOffset="4" endLine="126" endOffset="34"/></Target></Targets></Layout>