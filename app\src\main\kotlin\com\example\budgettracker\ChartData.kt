package com.example.budgettracker

class ChartData(
    var date: String,
    var amount: Int
) {
    // Getters and Setters
    fun getDate(): String = date

    fun setDate(date: String) {
        this.date = date
    }

    fun getAmount(): Int = amount

    fun setAmount(amount: Int) {
        this.amount = amount
    }

    fun getXValue(): Int = date.replace("/", "").toInt()

    fun getYValue(): Double = amount.toDouble()
}
