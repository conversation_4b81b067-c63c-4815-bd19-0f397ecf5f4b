<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:id="@+id/root_layout"
    android:padding="16dp">

    <!-- Welcome Icon -->
    <ImageView
        android:id="@+id/welcome_icon"
        android:layout_width="174dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:contentDescription="@string/todo"
        android:src="@drawable/logos" />

    <!-- Description -->
    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/your_personal_budget_tracker"
        android:textColor="#111811"
        android:textSize="10sp"
        android:layout_below="@id/welcome_icon"
        android:layout_marginTop="16dp"
        android:layout_centerHorizontal="true"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        tools:ignore="SmallSp" />

    <!-- Email Container -->
    <LinearLayout
        android:id="@+id/email_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/description"
        android:layout_marginTop="24dp"
        android:paddingLeft="16dp"
        android:background="@drawable/edittext_background"
        android:paddingRight="16dp">

        <EditText
            android:id="@+id/email_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints=""
            android:hint="@string/enter_email"
            android:background="@android:color/transparent"
            android:padding="16dp"
            android:selectAllOnFocus="true"
            android:singleLine="false"
            android:textColor="#111811"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Password Container -->
    <LinearLayout
        android:id="@+id/password_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_below="@id/email_container"
        android:background="@drawable/edittext_background"
        android:layout_marginTop="16dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <EditText
            android:id="@+id/password_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:autofillHints=""
            android:hint="@string/enter_password"
            android:background="@android:color/transparent"
            android:textColor="#111811"
            android:padding="16dp"
            android:textSize="14sp"
            android:inputType="textPassword" />

        <ImageView
            android:id="@+id/password_eye"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/todo"
            android:src="@drawable/eye_icon"
            android:padding="16dp" />
    </LinearLayout>

    <!-- Login Button -->
    <Button
        android:id="@+id/login_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/password_container"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="24dp"
        android:background="@color/steelblue"
        android:padding="12dp"
        android:text="@string/log_in"
        android:textSize="19sp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:gravity="center"
        />

    <!-- Or Log In With -->
    <TextView
        android:id="@+id/or_login_with"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/or_log_in_with"
        android:textColor="#638864"
        android:textSize="12sp"
        android:layout_below="@id/login_button"
        android:layout_marginTop="16dp"
        android:layout_centerHorizontal="true" />

    <!-- Fingerprint Instead -->

    <ImageView
        android:id="@+id/fingerprint_icon"
        android:layout_width="96dp"
        android:layout_height="88dp"
        android:layout_below="@+id/fingerprint_instead"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:clickable="true"
        android:contentDescription="@string/fingerprint_icon_description"
        android:focusable="true"
        android:src="@drawable/fingerprint" />

    <TextView
        android:id="@+id/fingerprint_instead"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_below="@id/or_login_with"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:clickable="true"
        android:focusable="true"
        android:text="@string/touch_id"
        android:textColor="#215BF0"
        android:textSize="11sp" />

    <TextView
        android:id="@+id/signup_instead"
        android:layout_width="154dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/fingerprint_icon"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="136dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="136dp"
        android:clickable="true"
        android:focusable="true"
        android:text="@string/sign_up"
        android:textColor="#215BF0"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@id/fingerprint_icon" />

    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">

        <include
            layout="@layout/spinner" />
    </RelativeLayout>
</RelativeLayout>
