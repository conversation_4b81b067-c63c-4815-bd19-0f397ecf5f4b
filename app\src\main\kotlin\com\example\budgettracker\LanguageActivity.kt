package com.example.budgettracker

import android.content.Intent
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Bundle
import android.widget.ImageButton
import android.widget.RadioGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import java.util.*

class LanguageActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.language)

        val radioGroup = findViewById<RadioGroup>(R.id.radio_group)

        radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_english -> setLocale("en")
                R.id.radio_afrikaans -> setLocale("af")
                R.id.radio_zulu -> setLocale("zu")
            }
        }

        // Back button
        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@LanguageActivity, SettingsActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setLocale(languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        val config = Configuration()
        config.setLocale(locale)
        resources.updateConfiguration(config, resources.displayMetrics)
        
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val editor = prefs.edit()
        editor.putString("language", languageCode)
        editor.apply()
        
        val languageName = when (languageCode) {
            "en" -> "English"
            "af" -> "Afrikaans"
            "zu" -> "Zulu"
            else -> languageCode
        }
        
        Toast.makeText(this, "Language changed to $languageName", Toast.LENGTH_SHORT).show()
        
        // Restart the activity
        val intent = intent
        finish()
        startActivity(intent)
    }
}
