package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.util.*;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageButton;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import android.app.DatePickerDialog;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import retrofit2.Call;

public class ExpensesActivity extends AppCompatActivity {

    private EditText editText2, editText4;
    private TextView editText3;
    private ApiService apiService;
    private String selectedCategory = "Select Category";
    private final String selectedDate = "";
    private DatabaseHelper dbHelper;
    private SessionManager sessionManager;
    public String user_id;
    private LoadingSpinner loadingSpinner;
    @SuppressLint("WrongViewCast")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.addexpense);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this, rootLayout);
        // Initialize ApiService
        Object ApiClient = null;
        // Initialize views
        editText3 = findViewById(R.id.editText3);
        editText2 = findViewById(R.id.editText2);
        editText4 = findViewById(R.id.editText4);
        Spinner categorySpinner = findViewById(R.id.category_spinner);
        Button saveButton = findViewById(R.id.save_button);
        Button datePickerButton = findViewById(R.id.date_picker_button);
        dbHelper = new DatabaseHelper(this);

        sessionManager = new SessionManager(this);
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid();
        }

        //back button
        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {

            Intent intent = new Intent(ExpensesActivity.this, DashboardActivity.class);
            startActivity(intent);
        });
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        String currentDate = dateFormat.format(calendar.getTime());
        editText3.setText(currentDate);

        editText4.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });

        editText4.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });
        editText2.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });


        // Set up Spinner for categories
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                R.array.categories_array, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(adapter);
        categorySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                selectedCategory = parent.getItemAtPosition(position).toString();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedCategory = "Select Category";
            }
        });
        datePickerButton.setOnClickListener(v -> showDatePicker());
        saveButton.setOnClickListener(v -> {

            String note = editText2.getText().toString();
            String amountString = editText4.getText().toString();
            String date = editText3.getText().toString();
            String selectedCategory = categorySpinner.getSelectedItem().toString();

            if (note.isEmpty() || amountString.isEmpty() || date.isEmpty() || selectedCategory.equals("Select Category")) {
                Toast.makeText(ExpensesActivity.this, "Please fill in all fields", Toast.LENGTH_SHORT).show();

                return;
            }
            int budgetAmount;
            try {
                budgetAmount = Integer.parseInt(amountString);
            } catch (NumberFormatException e) {
                Toast.makeText(ExpensesActivity.this, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show();

                return;
            }
            int budgetAmountx = 0;
            if (NetworkUtils.isInternetAvailable(this)) {
                loadingSpinner.show();
                ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
                Map<String, Object> data = new HashMap<>();
                data.put("user_id", String.valueOf(user_id));
                data.put("name",selectedCategory);
                data.put("date", date);
                data.put("note", note);
                data.put( "amount", budgetAmount);
                data.put("budget_amount", budgetAmount);;
                Call<Void> call =  apiService.createRecord("expenses", data);
                call.enqueue(new retrofit2.Callback<Void>() {
                    @Override
                    public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                        if (response.isSuccessful()) {
                            String monthYear = date.substring(0, 7);
                            updateTransactionOnline(monthYear, budgetAmount);
                            Toast.makeText(ExpensesActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();
                            loadingSpinner.hide();
                            Intent intent = getIntent();
                            finish();
                            startActivity(intent);
                        } else {
                            Toast.makeText(ExpensesActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                        Toast.makeText(ExpensesActivity.this, "Network error. Saving locally.", Toast.LENGTH_SHORT).show();
                        SQLiteDatabase db = dbHelper.getWritableDatabase();
                        saveDataToDatabase(db, selectedCategory, date, note, budgetAmount, budgetAmount);

                    }
                });
            }else{
                SQLiteDatabase db = dbHelper.getWritableDatabase();
                saveDataToDatabase(db,selectedCategory, date, note, budgetAmount, budgetAmountx);

            }
            });
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToDatabase(SQLiteDatabase db,String name, String date, String note, int amount, int budgetAmount) {
        ContentValues values = new ContentValues();
        values.put("name", name);
        values.put("date", date);
        values.put( "note", note);
        values.put( "amount", amount);
        values.put("budget_amount", budgetAmount);
        values.put("user_id", user_id);

        long newRowId = db.insert("expenses", null, values);
        if (newRowId == -1) {
            Toast.makeText(ExpensesActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
        } else {
            String monthYear =date.substring(0, 7);
            updateTransactionBalance(db, monthYear, amount);
            Toast.makeText(ExpensesActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();
            Intent intent = getIntent();
            finish();
            startActivity(intent);
        }
        db.close();
    }


    private void updateTransactionBalance(SQLiteDatabase db, String monthYear, int amount) {
        String balanceQuery = "SELECT trans_balance FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE  ? AND user_id = ?";
        Cursor cursor = db.rawQuery(balanceQuery, new String[]{monthYear, String.valueOf(user_id)});
        float currentBalance = 0;
        if (cursor.moveToFirst()) {
            currentBalance = cursor.getFloat(0);
            cursor.close();
            float newBalance = currentBalance - amount;
            ContentValues values = new ContentValues();
            values.put("trans_balance", newBalance);
            int rowsUpdated = db.update("transactions", values, "strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?", new String[]{monthYear,String.valueOf(user_id)});
            if (rowsUpdated == 0) {Log.w("DatabaseHelper", "No rows updated in transactions table.");}
        }
    }
    private void updateTransactionOnline(String monthYear, int amount) {
        loadingSpinner.show();

        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);

        Call<List<Record>> fetchCall = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
        fetchCall.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();
                    boolean updated = false;

                    for (Record record : records) {
                        if (record instanceof Transaction) {
                            Transaction transaction = (Transaction) record;
                            float currentBalance = (float) transaction.getBalance();
                            float amountx = currentBalance - amount;
                            Map<String, Object> data = new HashMap<>();
                            data.put("trans_balance", amountx);
                            Call<Void> updateCall = apiService.updateRecord("transactions", user_id, monthYear, data);
                            updateCall.enqueue(new retrofit2.Callback<Void>() {
                                @Override
                                public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                                    if (response.isSuccessful()) {
                                        Log.i("SyncManager", "Transaction balance updated successfully");
                                    } else {
                                        Log.e("SyncManager", "Failed to update transaction balance: " + response.message());
                                    }
                                    loadingSpinner.hide();
                                }

                                @Override
                                public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                                    Log.e("UpdateTransaction", "Network error updating transaction: " + t.getMessage());
                                    loadingSpinner.hide();
                                }
                            });
                            updated = true;
                            break;
                        }
                    }
                    if (!updated) {
                        Log.e("UpdateTransaction", "No transaction found to update.");
                    }

                } else {
                    Log.e("UpdateTransaction", "Error fetching transaction: " + response.message());
                    loadingSpinner.hide();
                }
            }

            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                Log.e("SyncManager", "Error updating transaction balance: " + t.getMessage());
                loadingSpinner.hide();
            }
        });
    }


    private void showDatePicker() {
        final Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                (view, selectedYear, selectedMonth, selectedDay) -> {
                    Calendar selectedDate = Calendar.getInstance();
                    selectedDate.set(selectedYear, selectedMonth, selectedDay);
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                    String selectedDateString = dateFormat.format(selectedDate.getTime());
                    editText3.setText(selectedDateString);
                }, year, month, day);
        datePickerDialog.show();
    }

}
