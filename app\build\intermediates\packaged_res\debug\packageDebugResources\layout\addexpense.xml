<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:id="@+id/root_layout"
    android:padding="16dp">

    <TextView
        android:id="@+id/editText3"
        android:layout_width="174dp"
        android:layout_height="66dp"
        android:layout_below="@+id/date_picker_button"
        android:layout_marginTop="-63dp"
        android:gravity="center"
        android:text="@string/date_x"
        android:textColor="#111418"
        android:textSize="23sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.964"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="240dp"
        android:layout_height="74dp"
        android:layout_marginTop="8dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/add_a_new_expense"
        android:textColor="#111418"
        android:textSize="23sp" />

    <ImageButton
        android:id="@+id/back_button"
        android:layout_width="92dp"
        android:layout_height="80dp"
        android:layout_marginTop="8dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:background="@drawable/button_background"
        android:contentDescription="@string/back_description"
        android:src="@drawable/back"
        android:textStyle="bold"
        app:tint="@color/black" />

    <EditText
        android:id="@+id/editText2"
        android:layout_width="336dp"
        android:layout_height="135dp"
        android:layout_below="@+id/editText4"
        android:layout_marginTop="111dp"
        android:background="@drawable/edittext_background"
        android:hint="@string/add_a_note"
        android:padding="15dp"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/editText4"
        android:layout_width="174dp"
        android:layout_height="81dp"
        android:layout_below="@+id/date_picker_button"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="-387dp"
        android:layout_toStartOf="@+id/editText3"
        android:background="@drawable/edittext_background"
        android:hint="@string/_0_00"
        android:padding="15dp"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/save_button"
        android:layout_width="336dp"
        android:layout_height="55dp"
        android:layout_below="@+id/editText2"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="220dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/backg_button"
        android:gravity="center"
        android:padding="12dp"
        android:text="@string/save"
        android:textColor="#FFFFFF"
        android:textSize="19sp"
        android:textStyle="bold" />

    <Spinner
        android:id="@+id/category_spinner"
        android:layout_width="134dp"
        android:layout_height="72dp"
        android:layout_below="@+id/textView"
        android:layout_alignParentStart="true"
        android:layout_marginStart="60dp"
        android:layout_marginTop="132dp"
        android:layout_marginEnd="36dp"
        android:layout_toStartOf="@+id/date_picker_button"
        android:layout_toEndOf="@+id/textView"
        android:background="@color/white"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/date_picker_button"
        android:layout_width="153dp"
        android:layout_height="67dp"
        android:layout_below="@+id/textView"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/backg_button"
        android:gravity="center"
        android:padding="12dp"
        android:text="@string/pick_date"
        android:textColor="#FFFFFF"
        android:textSize="19sp"
        android:textStyle="bold" />

    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">
        <include
            layout="@layout/spinner" />
    </RelativeLayout>

</RelativeLayout>

<!--<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:background="@android:color/white"-->
<!--    android:id="@+id/root_layout"-->
<!--    android:padding="16dp"-->
<!--    tools:ignore="DuplicateIds">-->


<!--    <TextView-->
<!--        android:id="@+id/editText3"-->
<!--        android:layout_width="208dp"-->
<!--        android:layout_height="47dp"-->
<!--        android:layout_marginTop="16dp"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/date_x"-->
<!--        android:textColor="#111418"-->
<!--        android:textSize="23sp"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.964"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/textView"-->
<!--        tools:ignore="MissingConstraints" />-->

<!--    <EditText-->
<!--        android:id="@+id/editText2"-->
<!--        android:layout_width="336dp"-->
<!--        android:layout_height="135dp"-->
<!--        android:layout_marginTop="40dp"-->
<!--        android:background="@drawable/edittext_background"-->
<!--        android:hint="@string/add_a_note"-->
<!--        android:padding="15dp"-->
<!--        android:textStyle="bold"-->
<!--        android:layout_weight="1"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="20sp"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/editText4"-->
<!--        tools:ignore="MissingConstraints"-->
<!--        tools:layout_editor_absoluteX="16dp" />-->

<!--    <Spinner-->
<!--        android:id="@+id/category_spinner"-->
<!--        android:layout_width="149dp"-->
<!--        android:layout_height="67dp"-->
<!--        android:layout_marginTop="76dp"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="18sp"-->
<!--        android:background="@color/white"-->
<!--        android:textStyle="bold"-->
<!--        android:layout_weight="1"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/textView"-->
<!--        tools:ignore="MissingConstraints"-->
<!--        tools:layout_editor_absoluteX="16dp" />-->

<!--    <ImageButton-->
<!--        android:id="@+id/back_button"-->
<!--        android:layout_width="92dp"-->
<!--        android:layout_height="80dp"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:background="@drawable/button_background"-->
<!--        android:contentDescription="@string/back_description"-->
<!--        android:src="@drawable/back"-->
<!--        android:tint="@color/black"-->
<!--        android:textStyle="bold"-->
<!--        android:layout_weight="1"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.0"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        tools:ignore="UseAppTint" />-->

<!--    <TextView-->
<!--        android:id="@+id/textView"-->
<!--        android:layout_width="240dp"-->
<!--        android:layout_height="74dp"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:gravity="center"-->
<!--        android:text="@string/add_a_new_expense"-->
<!--        android:textColor="#111418"-->
<!--        android:textSize="23sp"-->
<!--        android:layout_weight="1"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.496"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        tools:ignore="MissingConstraints,UnknownId" />-->

<!--    <EditText-->
<!--        android:id="@+id/editText4"-->
<!--        android:layout_width="348dp"-->
<!--        android:layout_height="61dp"-->
<!--        android:layout_marginTop="76dp"-->
<!--        android:layout_weight="1"-->
<!--        android:background="@drawable/edittext_background"-->
<!--        android:hint="@string/_0_00"-->
<!--        android:padding="15dp"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="20sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.29"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/category_spinner"-->
<!--        tools:ignore="MissingConstraints" />-->

<!--    <Button-->
<!--        android:id="@+id/save_button"-->
<!--        android:layout_width="336dp"-->
<!--        android:layout_height="55dp"-->
<!--        android:background="@drawable/backg_button"-->
<!--        android:gravity="center"-->
<!--        android:padding="12dp"-->
<!--        android:text="@string/save"-->
<!--        android:textColor="#FFFFFF"-->
<!--        android:textSize="19sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.488"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/editText2"-->
<!--        app:layout_constraintVertical_bias="0.865"-->
<!--        tools:ignore="MissingConstraints" />-->

<!--    <Button-->
<!--        android:id="@+id/date_picker_button"-->
<!--        android:layout_width="153dp"-->
<!--        android:layout_height="67dp"-->
<!--        android:layout_marginTop="76dp"-->
<!--        android:background="@drawable/backg_button"-->
<!--        android:gravity="center"-->
<!--        android:padding="12dp"-->
<!--        android:text="@string/pick_date"-->
<!--        android:textColor="#FFFFFF"-->
<!--        android:textSize="19sp"-->
<!--        android:textStyle="bold"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.902"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/textView" />-->

<!--    <RelativeLayout-->
<!--        android:id="@+id/spinner_container"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:visibility="gone"-->
<!--        android:gravity="center">-->

<!--        <include-->
<!--            layout="@layout/spinner" />-->
<!--    </RelativeLayout>-->
<!--&lt;!&ndash;</androidx.constraintlayout.widget.ConstraintLayout>&ndash;&gt;-->
<!--</RelativeLayout>-->