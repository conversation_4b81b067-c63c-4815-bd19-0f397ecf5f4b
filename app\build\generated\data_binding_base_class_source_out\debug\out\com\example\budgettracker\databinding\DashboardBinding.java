// Generated by view binder compiler. Do not edit!
package com.example.budgettracker.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.budgettracker.R;
import com.jjoe64.graphview.GraphView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DashboardBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView budgetId;

  @NonNull
  public final ImageButton buttonLogout;

  @NonNull
  public final ImageButton buttonNotification;

  @NonNull
  public final TextView firstTextView;

  @NonNull
  public final BaseBinding footer;

  @NonNull
  public final GraphView graph;

  @NonNull
  public final TextView itemName;

  @NonNull
  public final LinearLayout linearLayout2;

  @NonNull
  public final TextView month;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final RelativeLayout rootLayout;

  @NonNull
  public final TextView secondTextView;

  @NonNull
  public final RelativeLayout spinnerContainer;

  private DashboardBinding(@NonNull RelativeLayout rootView, @NonNull TextView budgetId,
      @NonNull ImageButton buttonLogout, @NonNull ImageButton buttonNotification,
      @NonNull TextView firstTextView, @NonNull BaseBinding footer, @NonNull GraphView graph,
      @NonNull TextView itemName, @NonNull LinearLayout linearLayout2, @NonNull TextView month,
      @NonNull RecyclerView recyclerView, @NonNull RelativeLayout rootLayout,
      @NonNull TextView secondTextView, @NonNull RelativeLayout spinnerContainer) {
    this.rootView = rootView;
    this.budgetId = budgetId;
    this.buttonLogout = buttonLogout;
    this.buttonNotification = buttonNotification;
    this.firstTextView = firstTextView;
    this.footer = footer;
    this.graph = graph;
    this.itemName = itemName;
    this.linearLayout2 = linearLayout2;
    this.month = month;
    this.recyclerView = recyclerView;
    this.rootLayout = rootLayout;
    this.secondTextView = secondTextView;
    this.spinnerContainer = spinnerContainer;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.budget_id;
      TextView budgetId = ViewBindings.findChildViewById(rootView, id);
      if (budgetId == null) {
        break missingId;
      }

      id = R.id.button_logout;
      ImageButton buttonLogout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLogout == null) {
        break missingId;
      }

      id = R.id.button_notification;
      ImageButton buttonNotification = ViewBindings.findChildViewById(rootView, id);
      if (buttonNotification == null) {
        break missingId;
      }

      id = R.id.firstTextView;
      TextView firstTextView = ViewBindings.findChildViewById(rootView, id);
      if (firstTextView == null) {
        break missingId;
      }

      id = R.id.footer;
      View footer = ViewBindings.findChildViewById(rootView, id);
      if (footer == null) {
        break missingId;
      }
      BaseBinding binding_footer = BaseBinding.bind(footer);

      id = R.id.graph;
      GraphView graph = ViewBindings.findChildViewById(rootView, id);
      if (graph == null) {
        break missingId;
      }

      id = R.id.itemName;
      TextView itemName = ViewBindings.findChildViewById(rootView, id);
      if (itemName == null) {
        break missingId;
      }

      id = R.id.linearLayout2;
      LinearLayout linearLayout2 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout2 == null) {
        break missingId;
      }

      id = R.id.month;
      TextView month = ViewBindings.findChildViewById(rootView, id);
      if (month == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.root_layout;
      RelativeLayout rootLayout = ViewBindings.findChildViewById(rootView, id);
      if (rootLayout == null) {
        break missingId;
      }

      id = R.id.secondTextView;
      TextView secondTextView = ViewBindings.findChildViewById(rootView, id);
      if (secondTextView == null) {
        break missingId;
      }

      id = R.id.spinner_container;
      RelativeLayout spinnerContainer = ViewBindings.findChildViewById(rootView, id);
      if (spinnerContainer == null) {
        break missingId;
      }

      return new DashboardBinding((RelativeLayout) rootView, budgetId, buttonLogout,
          buttonNotification, firstTextView, binding_footer, graph, itemName, linearLayout2, month,
          recyclerView, rootLayout, secondTextView, spinnerContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
