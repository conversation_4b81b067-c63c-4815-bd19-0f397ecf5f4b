package com.example.budgettracker

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.ContentValues
import android.content.Intent
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.*
import androidx.annotation.NonNull
import androidx.appcompat.app.AppCompatActivity
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class ExpensesActivity : AppCompatActivity() {

    private lateinit var editText2: EditText
    private lateinit var editText4: EditText
    private lateinit var editText3: TextView
    private lateinit var apiService: ApiService
    private var selectedCategory = "Select Category"
    private val selectedDate = ""
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var sessionManager: SessionManager
    private var user_id: String? = null
    private lateinit var loadingSpinner: LoadingSpinner

    @SuppressLint("WrongViewCast")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.addexpense)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        
        // Initialize views
        editText3 = findViewById(R.id.editText3)
        editText2 = findViewById(R.id.editText2)
        editText4 = findViewById(R.id.editText4)
        val categorySpinner = findViewById<Spinner>(R.id.category_spinner)
        val saveButton = findViewById<Button>(R.id.save_button)
        val datePickerButton = findViewById<Button>(R.id.date_picker_button)
        dbHelper = DatabaseHelper(this)

        sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid()
        }

        // Back button
        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@ExpensesActivity, DashboardActivity::class.java)
            startActivity(intent)
        }
        
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val currentDate = dateFormat.format(calendar.time)
        editText3.text = currentDate

        editText4.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick()
                true
            } else {
                false
            }
        }

        // Set up category spinner
        val categories = arrayOf("Select Category", "Food", "Transport", "Entertainment", "Shopping", "Bills", "Other")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categories)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        categorySpinner.adapter = adapter

        categorySpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, view: android.view.View?, position: Int, id: Long) {
                selectedCategory = parent.getItemAtPosition(position).toString()
            }

            override fun onNothingSelected(parent: AdapterView<*>) {
                // Do nothing
            }
        }

        datePickerButton.setOnClickListener { showDatePicker() }
        
        saveButton.setOnClickListener {
            val note = editText2.text.toString()
            val amountString = editText4.text.toString()
            val date = editText3.text.toString()
            val selectedCategory = categorySpinner.selectedItem.toString()

            if (note.isEmpty() || amountString.isEmpty() || date.isEmpty() || selectedCategory == "Select Category") {
                Toast.makeText(this@ExpensesActivity, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            val budgetAmount = try {
                amountString.toInt()
            } catch (e: NumberFormatException) {
                Toast.makeText(this@ExpensesActivity, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            val budgetAmountx = 0
            if (NetworkUtils.isInternetAvailable(this)) {
                loadingSpinner.show()
                val apiService = RetrofitClient.getClient().create(ApiService::class.java)
                val data = hashMapOf<String, Any>(
                    "user_id" to user_id.toString(),
                    "name" to selectedCategory,
                    "date" to date,
                    "note" to note,
                    "amount" to budgetAmount,
                    "budget_amount" to budgetAmount
                )
                
                val call = apiService.createRecord("expenses", data)
                call.enqueue(object : Callback<Void> {
                    override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                        if (response.isSuccessful) {
                            val monthYear = date.substring(0, 7)
                            updateTransactionOnline(monthYear, budgetAmount)
                            Toast.makeText(this@ExpensesActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()
                            loadingSpinner.hide()
                            val intent = getIntent()
                            finish()
                            startActivity(intent)
                        } else {
                            Toast.makeText(this@ExpensesActivity, "Error saving data", Toast.LENGTH_SHORT).show()
                        }
                    }

                    override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                        Toast.makeText(this@ExpensesActivity, "Network error. Saving locally.", Toast.LENGTH_SHORT).show()
                        val db = dbHelper.writableDatabase
                        saveDataToDatabase(db, selectedCategory, date, note, budgetAmount, budgetAmount)
                    }
                })
            } else {
                val db = dbHelper.writableDatabase
                saveDataToDatabase(db, selectedCategory, date, note, budgetAmount, budgetAmountx)
            }
        }
    }

    @SuppressLint("UnsafeIntentLaunch")
    private fun saveDataToDatabase(db: SQLiteDatabase, name: String, date: String, note: String, amount: Int, budgetAmount: Int) {
        val values = ContentValues().apply {
            put("name", name)
            put("date", date)
            put("note", note)
            put("amount", amount)
            put("budget_amount", budgetAmount)
            put("user_id", user_id)
        }

        val newRowId = db.insert("expenses", null, values)
        if (newRowId == -1L) {
            Toast.makeText(this@ExpensesActivity, "Error saving data", Toast.LENGTH_SHORT).show()
        } else {
            val monthYear = date.substring(0, 7)
            updateTransactionBalance(db, monthYear, amount)
            Toast.makeText(this@ExpensesActivity, "Data saved successfully", Toast.LENGTH_SHORT).show()
            val intent = getIntent()
            finish()
            startActivity(intent)
        }
        db.close()
    }

    private fun updateTransactionBalance(db: SQLiteDatabase, monthYear: String, amount: Int) {
        val updateQuery = "UPDATE transactions SET trans_balance = trans_balance + ? WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?"
        db.execSQL(updateQuery, arrayOf(amount, monthYear, user_id.toString()))
        
        val cursor = db.rawQuery(
            "SELECT changes() AS affected_rows",
            null
        )
        if (cursor.moveToFirst()) {
            val rowsUpdated = cursor.getInt(0)
            if (rowsUpdated == 0) {
                Log.w("DatabaseHelper", "No rows updated in transactions table.")
            }
        }
        cursor.close()
    }

    private fun updateTransactionOnline(monthYear: String, amount: Int) {
        loadingSpinner.show()

        val apiService = RetrofitClient.getClient().create(ApiService::class.java)

        val fetchCall = apiService.getRecords("transactions", monthYear, user_id.toString())
        fetchCall.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var updated = false

                    for (record in records) {
                        if (record is Transaction) {
                            val newBalance = record.getBalance() + amount
                            val data = hashMapOf<String, Any>(
                                "trans_balance" to newBalance,
                                "user_id" to user_id!!
                            )

                            val updateCall = apiService.updateRecord("transactions", user_id!!, monthYear, data)
                            updateCall.enqueue(object : Callback<Void> {
                                override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                                    if (response.isSuccessful) {
                                        Log.d("ExpensesActivity", "Transaction balance updated successfully")
                                    } else {
                                        Log.e("ExpensesActivity", "Failed to update transaction balance")
                                    }
                                }

                                override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                                    Log.e("ExpensesActivity", "Network error updating transaction: ${t.message}")
                                }
                            })
                            updated = true
                            break
                        }
                    }

                    if (!updated) {
                        Log.w("ExpensesActivity", "No transaction found to update for month: $monthYear")
                    }
                } else {
                    Log.e("ExpensesActivity", "Failed to fetch transactions for update")
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Log.e("ExpensesActivity", "Network error fetching transactions: ${t.message}")
            }
        })
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(
            this,
            { _, selectedYear, selectedMonth, selectedDay ->
                val selectedDate = String.format(
                    Locale.getDefault(),
                    "%04d-%02d-%02d",
                    selectedYear,
                    selectedMonth + 1,
                    selectedDay
                )
                editText3.text = selectedDate
            },
            year,
            month,
            day
        )
        datePickerDialog.show()
    }
}
