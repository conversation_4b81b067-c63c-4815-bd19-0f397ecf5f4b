<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="base" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\base.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/base_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="56" endOffset="14"/></Target><Target id="@+id/button_home" view="ImageButton"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="33"/></Target><Target id="@+id/button_expenses" view="ImageButton"><Expressions/><location startLine="22" startOffset="4" endLine="31" endOffset="33"/></Target><Target id="@+id/button_transactions" view="ImageButton"><Expressions/><location startLine="34" startOffset="4" endLine="43" endOffset="33"/></Target><Target id="@+id/button_profile" view="ImageButton"><Expressions/><location startLine="46" startOffset="4" endLine="55" endOffset="33"/></Target></Targets></Layout>