<?xml version="1.0" encoding="utf-8"?>
   <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
       xmlns:app="http://schemas.android.com/apk/res-auto"
       xmlns:tools="http://schemas.android.com/tools"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:background="@android:color/white"
       android:id="@+id/root_layout"
        android:padding="16dp"
       >

        <!-- Header Section -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!-- Back Button -->

            <!-- Title -->
            <ImageButton
                android:id="@+id/back_button"
                android:layout_width="52dp"
                android:layout_height="43dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/button_background"
                android:contentDescription="@string/back_description"
                android:src="@drawable/back"
                android:tint="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="UseAppTint" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/back_button"
                android:layout_marginStart="70dp"
                android:layout_marginTop="-40dp"
                android:text="Create Account"
                android:textColor="@color/text_color"
                android:textSize="28sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <!-- Radio Buttons -->

        <!-- Apply Button -->

       <LinearLayout
           android:layout_width="391dp"
           android:layout_height="461dp"
           android:layout_marginTop="100dp"
           android:orientation="vertical"
           android:padding="16dp"
           tools:ignore="UnknownId">

           <LinearLayout
               android:id="@+id/username_container"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_below="@id/description"
               android:layout_marginTop="24dp"
               android:background="@drawable/edittext_background"
               android:orientation="vertical"
               android:paddingLeft="16dp"
               android:paddingRight="16dp">

               <EditText
                   android:id="@+id/username_input"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:background="@android:color/transparent"
                   android:hint="Enter Username"
                   android:padding="16dp"
                   android:textColor="#111811"
                   android:textSize="14sp"
                   tools:ignore="HardcodedText" />
           </LinearLayout>

           <LinearLayout
               android:id="@+id/email_container"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_below="@id/description"
               android:layout_marginTop="24dp"
               android:orientation="vertical"
               android:paddingLeft="16dp"
               android:background="@drawable/edittext_background"
               android:paddingRight="16dp">

               <EditText
                   android:id="@+id/email_input"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:hint="Enter email"
                   android:background="@android:color/transparent"
                   android:padding="16dp"
                   android:textColor="#111811"
                   android:textSize="14sp"
                   tools:ignore="HardcodedText" />
           </LinearLayout>

           <!-- Password Container -->
           <LinearLayout
               android:id="@+id/password_container"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:background="@drawable/edittext_background"
               android:layout_marginTop="16dp"
               android:orientation="horizontal"
               android:paddingLeft="16dp"
               android:paddingRight="16dp">

               <EditText
                   android:id="@+id/password_input"
                   android:layout_width="0dp"
                   android:layout_height="wrap_content"
                   android:layout_weight="1"
                   android:background="@android:color/transparent"
                   android:hint="Enter password"
                   android:padding="16dp"
                   android:textColor="#111811"
                   android:inputType="textPassword"
                   android:textSize="14sp"
                   tools:ignore="HardcodedText" />

               <ImageView
                   android:id="@+id/password_eye"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:padding="16dp"
                   android:src="@drawable/eye_icon" />
           </LinearLayout>
           <!-- Password Container -->
           <LinearLayout
               android:id="@+id/password_container_confrim"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_below="@id/email_container"
               android:background="@drawable/edittext_background"
               android:layout_marginTop="16dp"
               android:orientation="horizontal"
               android:paddingLeft="16dp"
               android:paddingRight="16dp"
               tools:ignore="ObsoleteLayoutParam">

               <EditText
                   android:id="@+id/password_input_confrim"
                   android:layout_width="0dp"
                   android:layout_height="wrap_content"
                   android:layout_weight="1"
                   android:hint="Confrim password"
                   android:background="@android:color/transparent"
                   android:padding="16dp"
                   android:textColor="#111811"
                   android:inputType="textPassword"
                   android:textSize="14sp"
                   tools:ignore="HardcodedText" />

               <ImageView
                   android:id="@+id/password_eye_confrim"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:padding="16dp"
                   android:src="@drawable/eye_icon" />
           </LinearLayout>

           <Button
               android:id="@+id/signup_button"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_marginTop="24dp"
               android:background="@color/steelblue"
               android:gravity="center"
               android:padding="12dp"
               android:text="Sign up"
               android:textColor="@color/white"
               android:textSize="19sp"
               android:textStyle="bold"
               tools:ignore="HardcodedText" />
       </LinearLayout>
        <RelativeLayout
            android:id="@+id/spinner_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:gravity="center">

            <include
                layout="@layout/spinner" />
        </RelativeLayout>
   </RelativeLayout>
