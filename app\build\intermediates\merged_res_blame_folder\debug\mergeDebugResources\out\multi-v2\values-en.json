{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-57:/values-en/values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\BudgetTracker\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "64,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,63,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3333,1372,1659,1697,1769,1733,1841,1805,1561,1500,109,2739,720,1260,2776,3053,57,2589,2150,785,436,2405,2937,2205,3102,958,1011,540,3290,2454,163,2640,489,3147,2872,1604,1070,2541,1877,1916,295,3194,1113,334,663,1413,381,228,1311,2825,1219,832,1172,2262,597,2700,1452,2998,1975,2315,2030,871,3251", "endLines": "70,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,63,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17,62", "endColumns": "19,39,36,34,34,34,34,34,41,59,52,35,63,49,47,47,50,49,53,45,51,47,59,55,43,51,57,55,41,85,63,58,49,45,63,53,41,46,37,57,37,55,57,45,55,37,53,65,59,45,39,37,45,51,64,37,46,53,53,88,118,85,37", "endOffsets": "3547,1407,1691,1727,1799,1763,1871,1835,1598,1555,157,2770,779,1305,2819,3096,103,2634,2199,826,483,2448,2992,2256,3141,1005,1064,591,3327,2535,222,2694,534,3188,2931,1653,1107,2583,1910,1969,328,3245,1166,375,714,1446,430,289,1366,2866,1254,865,1213,2309,657,2733,1494,3047,2024,2399,2144,952,3284"}, "to": {"startLines": "2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,268,308,345,380,415,450,485,520,562,622,675,711,775,825,873,921,972,1022,1076,1122,1174,1222,1282,1338,1382,1434,1492,1548,1590,1676,1740,1799,1849,1895,1959,2013,2055,2102,2140,2198,2236,2292,2350,2396,2452,2490,2544,2610,2670,2716,2756,2794,2840,2892,2957,2995,3042,3096,3150,3239,3358,3444", "endLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70", "endColumns": "19,39,36,34,34,34,34,34,41,59,52,35,63,49,47,47,50,49,53,45,51,47,59,55,43,51,57,55,41,85,63,58,49,45,63,53,41,46,37,57,37,55,57,45,55,37,53,65,59,45,39,37,45,51,64,37,46,53,53,88,118,85,37", "endOffsets": "263,303,340,375,410,445,480,515,557,617,670,706,770,820,868,916,967,1017,1071,1117,1169,1217,1277,1333,1377,1429,1487,1543,1585,1671,1735,1794,1844,1890,1954,2008,2050,2097,2135,2193,2231,2287,2345,2391,2447,2485,2539,2605,2665,2711,2751,2789,2835,2887,2952,2990,3037,3091,3145,3234,3353,3439,3477"}}]}]}