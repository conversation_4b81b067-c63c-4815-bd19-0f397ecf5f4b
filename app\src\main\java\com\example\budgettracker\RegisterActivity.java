package com.example.budgettracker;

import android.content.ContentValues;
import android.content.Intent;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import retrofit2.Call;

public class RegisterActivity extends AppCompatActivity {

    private EditText emailEditText;
    private EditText passwordEditText;
    private EditText confirmPasswordEditText;
    private EditText usernameEdit;
    private Button applyButton;
    private DatabaseHelper dbHelper;
    private FirebaseAuth mAuth;
    private boolean isPasswordVisible = false;
    private LoadingSpinner loadingSpinner;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.register);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);
        loadingSpinner = new LoadingSpinner(this, rootLayout);
        dbHelper = new DatabaseHelper(this);
        mAuth = FirebaseAuth.getInstance();

        emailEditText = findViewById(R.id.email_input);
        passwordEditText = findViewById(R.id.password_input);
        confirmPasswordEditText = findViewById(R.id.password_input_confrim);
        usernameEdit = findViewById(R.id.username_input);
        ImageView passwordEyeImageView = findViewById(R.id.password_eye);
        ImageView confirmPasswordEyeImageView = findViewById(R.id.password_eye_confrim);
        applyButton = findViewById(R.id.signup_button);
        applyButton.setOnClickListener(v -> {

            registerUser();
//            new Handler().postDelayed(() -> loadingSpinner.hide(), 2000);
        });

        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(RegisterActivity.this, MainActivity.class);
            startActivity(intent);
        });

        passwordEyeImageView.setOnClickListener(v -> togglePasswordVisibility(passwordEditText, passwordEyeImageView));
        confirmPasswordEyeImageView.setOnClickListener(v -> togglePasswordVisibility(confirmPasswordEditText, confirmPasswordEyeImageView));

    }

    private void togglePasswordVisibility(EditText passwordEditText, ImageView eyeImageView) {
        if (isPasswordVisible) {
            passwordEditText.setTransformationMethod(PasswordTransformationMethod.getInstance());
            eyeImageView.setImageResource(R.drawable.eye_close);
        } else {
            passwordEditText.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            eyeImageView.setImageResource(R.drawable.eye_icon);
        }
        isPasswordVisible = !isPasswordVisible;
        passwordEditText.setSelection(passwordEditText.getText().length()); // Move cursor to end
    }

    private void registerUser() {
        String email = emailEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();
        String confirmPassword = confirmPasswordEditText.getText().toString().trim();
        String username = usernameEdit.getText().toString().trim();
        if (TextUtils.isEmpty(email)) {emailEditText.setError("Email is required");return;}
        if (TextUtils.isEmpty(username)) {usernameEdit.setError("Username is required");return;}
        if (TextUtils.isEmpty(password)) {passwordEditText.setError("Password is required");return;}
        if (!password.equals(confirmPassword)) {confirmPasswordEditText.setError("Passwords do not match");return;}

        mAuth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = mAuth.getCurrentUser();
                        if (user != null) {
                                if (NetworkUtils.isInternetAvailable(this)) {
                                    saveUserOnline(email, username,user.getUid());
                                }else{
                                    saveUserData(email, username,user.getUid());
                                }
                            new Handler().postDelayed(() -> {
                                Intent intent = new Intent(RegisterActivity.this, MainActivity.class);
                                startActivity(intent);
                                finish();
                            }, 2000);
                        }
                    } else {
                        Toast.makeText(RegisterActivity.this, "Registration failed: " + task.getException().getMessage(), Toast.LENGTH_SHORT).show();
                        loadingSpinner.hide();
                    }
                });
    }
    private void saveUserData(String email,String username, String uid) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put("user_email", email);
        values.put("user_name", username);
        values.put("user_id", uid);
        values.put("user_status", 0);

        long newRowId = db.insert("user", null, values);
        if (newRowId == -1) {
            loadingSpinner.hide();
            Toast.makeText(RegisterActivity.this, "Error saving user data", Toast.LENGTH_SHORT).show();
        } else {
            loadingSpinner.hide();
            Toast.makeText(RegisterActivity.this, "User data saved successfully", Toast.LENGTH_SHORT).show();
        }
        db.close();
    }
    private void saveUserOnline(String email,String username, String uid) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Map<String, Object> data = new HashMap<>();
        data.put("user_id", uid);
        data.put("user_email",email);
        data.put("user_name", username);
        data.put("user_status", 0);
        data.put("created_date", currentDate);
        Call<Void> call =  apiService.createRecord("user", data);
        call.enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    Toast.makeText(RegisterActivity.this, "Data saved successfully", Toast.LENGTH_SHORT).show();
                    loadingSpinner.hide();
                } else {
                    Toast.makeText(RegisterActivity.this, "Error saving data", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Toast.makeText(RegisterActivity.this, "Network error. Saving locally.", Toast.LENGTH_SHORT).show();
                saveUserData(email, username,uid);
            }
        });
    }
}
