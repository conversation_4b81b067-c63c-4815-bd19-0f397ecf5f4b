package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jjoe64.graphview.DefaultLabelFormatter
import com.jjoe64.graphview.GraphView
import com.jjoe64.graphview.GridLabelRenderer
import com.jjoe64.graphview.series.DataPoint
import com.jjoe64.graphview.series.LineGraphSeries
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.util.*

class DashboardActivity : AppCompatActivity() {
    
    private lateinit var apiService: ApiService
    private lateinit var listContainer: LinearLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var itemAdapter: ItemAdapter
    private lateinit var dbHelper: DatabaseHelper
    private var user_id: String? = null
    private var totalBudget = 0f
    private var balance = 0f
    private var trans_target = 0f
    private lateinit var loadingSpinner: LoadingSpinner

    @SuppressLint("WrongViewCast")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dashboard)
        
        dbHelper = DatabaseHelper(this)
        recyclerView = findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(this)
        
        val sessionManager = SessionManager(this)
        val secondTextView = findViewById<TextView>(R.id.secondTextView)
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        
        val sessionDetails = sessionManager.getAllSessionDetails()
        Log.w("MainActivity", "Session Details: $sessionDetails")
        
        if (sessionManager.isLoggedIn()) {
            val displayName = sessionManager.getUserName()
            if (displayName != null) {
                secondTextView.text = displayName
            }
            user_id = sessionManager.getUserUid()
        } else {
            val intent = Intent(this@DashboardActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
        }

        setupNavigationButtons()
        
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1
        val monthYear = String.format(Locale.getDefault(), "%d-%02d", year, month)

        if (NetworkUtils.isInternetAvailable(this)) {
            fetchAndSetOnline(monthYear)
        } else {
            fetchAndSetData(monthYear)
        }

        calculateBalanceForMonth(monthYear)

        val logoutButton = findViewById<ImageButton>(R.id.button_logout)
        logoutButton.setOnClickListener {
            sessionManager.logoutUser()
            val intent = Intent(this@DashboardActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
        }
    }

    private fun setupNavigationButtons() {
        val homeButton = findViewById<ImageButton>(R.id.button_home)
        val expensesButton = findViewById<ImageButton>(R.id.button_expenses)
        val transactionsButton = findViewById<ImageButton>(R.id.button_transactions)
        val profileButton = findViewById<ImageButton>(R.id.button_profile)
        val notificationButton = findViewById<ImageButton>(R.id.button_notification)

        homeButton.setOnClickListener { navigateTo(DashboardActivity::class.java) }
        expensesButton.setOnClickListener { navigateTo(ExpensesActivity::class.java) }
        transactionsButton.setOnClickListener { navigateTo(TransactionsActivity::class.java) }
        profileButton.setOnClickListener { navigateTo(SettingsActivity::class.java) }
        notificationButton.setOnClickListener { navigateTo(NotificationActivity::class.java) }
    }

    private fun calculateBalanceForMonth(monthYear: String) {
        if (NetworkUtils.isInternetAvailable(this)) {
            val apiService = RetrofitClient.getClient().create(ApiService::class.java)
            val call = apiService.getRecords("transactions", monthYear, user_id.toString())

            call.enqueue(object : Callback<List<Record>> {
                override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                    if (response.isSuccessful && response.body() != null && response.body()!!.isNotEmpty()) {
                        for (record in response.body()!!) {
                            if (record is Transaction) {
                                totalBudget = record.getTotal().toFloat()
                                balance = record.getBalance().toFloat()
                                trans_target = record.getTransTarget().toFloat()
                                break
                            }
                        }
                        updateUI()
                    } else {
                        Log.e("TransactionsActivity", "Response not successful or empty.")
                    }
                }

                override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                    Toast.makeText(this@DashboardActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
        } else {
            val result = fetchDataFromLocalDatabase(monthYear)
            balance = result.balance
            totalBudget = result.totalBudget
            trans_target = result.transTarget
            updateUI()
        }
    }

    private fun updateUI() {
        val budgetTextView = findViewById<TextView>(R.id.budget_id)
        @SuppressLint("DefaultLocale") 
        val budgetInfo = String.format("$%.2f", balance)
        budgetTextView.text = budgetInfo
        calculateAndDisplayPercentage(balance, trans_target)
    }

    private fun fetchDataFromLocalDatabase(monthYear: String): BalanceResult {
        val dbHelper = DatabaseHelper(this)
        val db = dbHelper.readableDatabase
        val transactionsQuery = "SELECT trans_budget, trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?"
        val transactionsCursor = db.rawQuery(transactionsQuery, arrayOf(monthYear, user_id.toString()))

        if (transactionsCursor.moveToFirst()) {
            trans_target = transactionsCursor.getFloat(2)
            totalBudget = transactionsCursor.getFloat(0)
            balance = transactionsCursor.getFloat(1)
        }
        transactionsCursor.close()
        db.close()
        return BalanceResult(totalBudget, balance, trans_target)
    }

    @SuppressLint("SetTextI18n")
    private fun calculateAndDisplayPercentage(actualAmount: Float, targetAmount: Float) {
        val result: String
        if (targetAmount != 0f) {
            val percentageAchieved = (actualAmount / targetAmount) * 100
            result = String.format(Locale.getDefault(), "%.2f%%", percentageAchieved)
            val monthTextView = findViewById<TextView>(R.id.month)
            when {
                percentageAchieved >= 100 -> {
                    monthTextView.setTextColor(Color.GREEN)
                }
                percentageAchieved >= 0 -> {
                    monthTextView.setTextColor(Color.BLUE)
                }
                else -> {
                    monthTextView.setTextColor(Color.RED)
                }
            }
            monthTextView.text = result
        } else {
            val monthTextView = findViewById<TextView>(R.id.month)
            monthTextView.text = "Target not set"
            monthTextView.setTextColor(Color.GRAY)
        }
    }

    private fun navigateTo(destination: Class<*>) {
        val intent = Intent(this@DashboardActivity, destination)
        startActivity(intent)
        finish()
    }

    private fun parseItems(json: String): List<Expenses> {
        val gson = Gson()
        val expensesListType: Type = object : TypeToken<List<Expenses>>() {}.type
        return gson.fromJson(json, expensesListType)
    }

    private fun parseChartData(json: String): List<ChartData> {
        val gson = Gson()
        val chartDataListType: Type = object : TypeToken<List<ChartData>>() {}.type
        return gson.fromJson(json, chartDataListType)
    }

    private fun setupChart(amounts: List<Int>) {
        val graph = findViewById<GraphView>(R.id.graph)

        val series = LineGraphSeries<DataPoint>()
        for (i in amounts.indices) {
            series.appendData(DataPoint(i.toDouble(), amounts[i].toDouble()), true, amounts.size)
        }

        series.color = Color.BLUE
        series.thickness = 8

        graph.addSeries(series)
        graph.viewport.isYAxisBoundsManual = true
        graph.viewport.setMinY(0.0)
        graph.viewport.setMaxY(amounts.maxOrNull()?.toDouble()?.plus(10) ?: 100.0)

        graph.viewport.isXAxisBoundsManual = true
        graph.viewport.setMinX(0.0)
        graph.viewport.setMaxX((amounts.size - 1).toDouble())

        graph.gridLabelRenderer.labelFormatter = object : DefaultLabelFormatter() {
            override fun formatLabel(value: Double, isValueX: Boolean): String {
                return if (isValueX) {
                    super.formatLabel(value, isValueX)
                } else {
                    super.formatLabel(value, isValueX)
                }
            }
        }

        graph.gridLabelRenderer.gridStyle = GridLabelRenderer.GridStyle.HORIZONTAL
        graph.gridLabelRenderer.isHorizontalLabelsVisible = false
    }

    private fun trimDate(date: String): String {
        return if (date.length >= 10) {
            date.substring(5, 10)
        } else {
            date
        }
    }

    private fun fetchAndSetData(monthyear: String) {
        val db = dbHelper.readableDatabase
        val projection = arrayOf("name", "note", "date", "amount", "user_id")
        val selection = "user_id = ? AND strftime('%Y-%m', date) = ?"
        val selectionArgs = arrayOf(user_id.toString(), monthyear)
        
        val cursor = db.query(
            "expenses",
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
        )

        val expensesList = mutableListOf<Expenses>()
        val amounts = mutableListOf<Int>()
        
        while (cursor.moveToNext()) {
            val name = cursor.getString(cursor.getColumnIndexOrThrow("name"))
            val date = cursor.getString(cursor.getColumnIndexOrThrow("date"))
            val note = cursor.getString(cursor.getColumnIndexOrThrow("note"))
            val amount = cursor.getInt(cursor.getColumnIndexOrThrow("amount"))
            val expense = Expenses(name, trimDate(date), note, amount, user_id!!)
            expensesList.add(expense)
            amounts.add(amount)
        }
        cursor.close()
        
        val gson = Gson()
        val jsonItems = gson.toJson(expensesList)
        
        if (amounts.isNotEmpty()) {
            setupChart(amounts)
        } else {
            val graph = findViewById<GraphView>(R.id.graph)
            graph.removeAllSeries()
        }
        
        val listType: Type = object : TypeToken<List<Expenses>>() {}.type
        val items: List<Expenses> = gson.fromJson(jsonItems, listType)
        val itemAdapter = ItemAdapter(items)
        recyclerView.adapter = itemAdapter
    }

    private fun fetchAndSetOnline(monthyear: String) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("expenses", monthyear, user_id.toString())
        
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val items = getExpenses(response)
                    if (items != null) {
                        for (expense in items) {
                            expense.setDate(trimDate(expense.getDate()))
                        }
                        val itemAdapter = ItemAdapter(items)
                        recyclerView.adapter = itemAdapter
                        loadingSpinner.hide()
                    } else {
                        Toast.makeText(applicationContext, "No expenses found", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(applicationContext, "Failed to fetch data", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Toast.makeText(applicationContext, "Error: ${t.message}", Toast.LENGTH_SHORT).show()
                loadingSpinner.hide()
            }
        })
    }

    @Nullable
    private fun getExpenses(@NonNull response: Response<List<Record>>): List<Expenses>? {
        val records = response.body()
        val expensesList = mutableListOf<Expenses>()
        val amounts = mutableListOf<Int>()
        
        for (record in records!!) {
            if (record is Expenses) {
                val amount = record.getAmount()
                expensesList.add(record)
                amounts.add(amount)
            }
        }
        
        val gson = Gson()
        val jsonItems = gson.toJson(expensesList)
        
        if (amounts.isNotEmpty()) {
            setupChart(amounts)
        } else {
            val graph = findViewById<GraphView>(R.id.graph)
            graph.removeAllSeries()
        }

        val listType: Type = object : TypeToken<List<Expenses>>() {}.type
        return gson.fromJson(jsonItems, listType)
    }
}
