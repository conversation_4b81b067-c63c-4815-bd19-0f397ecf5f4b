<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="login" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/login_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="179" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="169" startOffset="4" endLine="178" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="176" startOffset="8" endLine="177" endOffset="38"/></Target><Target id="@+id/welcome_icon" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="18" endOffset="39"/></Target><Target id="@+id/description" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="33" endOffset="32"/></Target><Target id="@+id/email_container" view="LinearLayout"><Expressions/><location startLine="36" startOffset="4" endLine="59" endOffset="18"/></Target><Target id="@+id/email_input" view="EditText"><Expressions/><location startLine="47" startOffset="8" endLine="58" endOffset="37"/></Target><Target id="@+id/password_container" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="93" endOffset="18"/></Target><Target id="@+id/password_input" view="EditText"><Expressions/><location startLine="73" startOffset="8" endLine="84" endOffset="46"/></Target><Target id="@+id/password_eye" view="ImageView"><Expressions/><location startLine="86" startOffset="8" endLine="92" endOffset="36"/></Target><Target id="@+id/login_button" view="Button"><Expressions/><location startLine="96" startOffset="4" endLine="110" endOffset="9"/></Target><Target id="@+id/or_login_with" view="TextView"><Expressions/><location startLine="113" startOffset="4" endLine="122" endOffset="48"/></Target><Target id="@+id/fingerprint_icon" view="ImageView"><Expressions/><location startLine="126" startOffset="4" endLine="137" endOffset="45"/></Target><Target id="@+id/fingerprint_instead" view="TextView"><Expressions/><location startLine="139" startOffset="4" endLine="150" endOffset="33"/></Target><Target id="@+id/signup_instead" view="TextView"><Expressions/><location startLine="152" startOffset="4" endLine="167" endOffset="68"/></Target></Targets></Layout>