<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dashboard" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dashboard_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="222" endOffset="16"/></Target><Target id="@+id/footer" tag="layout/dashboard_0" include="base"><Expressions/><location startLine="205" startOffset="4" endLine="210" endOffset="49"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="211" startOffset="4" endLine="220" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="218" startOffset="8" endLine="219" endOffset="38"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="55" endOffset="18"/></Target><Target id="@+id/firstTextView" view="TextView"><Expressions/><location startLine="19" startOffset="8" endLine="27" endOffset="37"/></Target><Target id="@+id/button_notification" view="ImageButton"><Expressions/><location startLine="29" startOffset="8" endLine="37" endOffset="37"/></Target><Target id="@+id/secondTextView" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="45" endOffset="37"/></Target><Target id="@+id/button_logout" view="ImageButton"><Expressions/><location startLine="46" startOffset="8" endLine="54" endOffset="37"/></Target><Target id="@+id/budget_id" view="TextView"><Expressions/><location startLine="93" startOffset="16" endLine="100" endOffset="46"/></Target><Target id="@+id/month" view="TextView"><Expressions/><location startLine="114" startOffset="20" endLine="119" endOffset="53"/></Target><Target id="@+id/graph" view="com.jjoe64.graphview.GraphView"><Expressions/><location startLine="124" startOffset="12" endLine="127" endOffset="47"/></Target><Target id="@+id/root_layout" view="RelativeLayout"><Expressions/><location startLine="169" startOffset="8" endLine="201" endOffset="24"/></Target><Target id="@+id/itemName" view="TextView"><Expressions/><location startLine="176" startOffset="12" endLine="183" endOffset="41"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="194" startOffset="16" endLine="198" endOffset="53"/></Target></Targets></Layout>