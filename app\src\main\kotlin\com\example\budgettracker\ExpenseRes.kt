package com.example.budgettracker

data class ExpenseRes(
    var success: Boolean = false,
    var message: String? = null
) {
    // Get<PERSON> and Setter for success
    fun isSuccess(): Boolean = success

    fun setSuccess(success: Boolean) {
        this.success = success
    }

    // Getter and Setter for message
    fun getMessage(): String? = message

    fun setMessage(message: String?) {
        this.message = message
    }
}
