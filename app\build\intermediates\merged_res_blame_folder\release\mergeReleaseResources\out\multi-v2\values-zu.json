{"logs": [{"outputFile": "com.example.budgettracker.app-mergeReleaseResources-57:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3901,4015", "endColumns": "113,122", "endOffsets": "4010,4133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2939,3031,3109,3165,3223,3289,3361,3438,3529,3612,3692,3771,3846,3925,4029,4119,4192,4286,4383,4457,4530,4629,4684,4768,4836,4924,5013,5075,5139,5202,5273,5382,5493,5596,5704,5764,5826", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2934,3026,3104,3160,3218,3284,3356,3433,3524,3607,3687,3766,3841,3920,4024,4114,4187,4281,4378,4452,4525,4624,4679,4763,4831,4919,5008,5070,5134,5197,5268,5377,5488,5591,5699,5759,5821,5903"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "365,4526,4604,4681,4758,4852,5049,5161,5389,8367,8492,11260,11335,11665,11757,11828,11893,11960,12032,12104,12158,12279,12338,12402,12456,12533,12761,12846,12927,13076,13163,13246,13388,13480,13558,13614,13672,13738,13810,13887,13978,14061,14141,14220,14295,14374,14478,14568,14641,14735,14832,14906,14979,15078,15133,15217,15285,15373,15462,15524,15588,15651,15722,15831,15942,16045,16153,16213,16668", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "531,4599,4676,4753,4847,4935,5156,5282,5465,8429,8590,11330,11393,11752,11823,11888,11955,12027,12099,12153,12274,12333,12397,12451,12528,12660,12841,12922,13071,13158,13241,13383,13475,13553,13609,13667,13733,13805,13882,13973,14056,14136,14215,14290,14369,14473,14563,14636,14730,14827,14901,14974,15073,15128,15212,15280,15368,15457,15519,15583,15646,15717,15826,15937,16040,16148,16208,16270,16745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,938,1045,1157,1245,1348,1463,1542,1619,1710,1803,1898,1992,2092,2185,2280,2374,2465,2558,2639,2743,2846,2944,3051,3158,3263,3420,17079", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "933,1040,1152,1240,1343,1458,1537,1614,1705,1798,1893,1987,2087,2180,2275,2369,2460,2553,2634,2738,2841,2939,3046,3153,3258,3415,3511,17156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,120", "endOffsets": "163,284"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "16275,16388", "endColumns": "112,120", "endOffsets": "16383,16504"}}, {"source": "C:\\Users\\<USER>\\Desktop\\BudgetTracker\\app\\src\\main\\res\\values-zu\\strings.xml", "from": {"startLines": "62,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3397,1436,1749,1787,1859,1823,1931,1895,1637,1575,111,2856,745,1320,2896,3207,57,2706,2250,818,453,2507,3070,2314,3256,991,1047,560,2557,171,2758,507,3302,3001,1680,1107,2658,1967,2006,306,3346,1149,351,684,1477,399,238,1376,2948,1277,865,1215,2378,619,2815,1517,3140,2065,2429,2131,904", "endLines": "68,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "endColumns": "19,39,36,34,34,34,34,34,41,60,58,38,71,54,50,47,52,50,62,45,52,48,68,62,44,54,58,57,99,65,55,51,42,67,67,40,46,37,57,43,49,64,46,59,38,52,66,58,51,41,37,60,49,63,39,56,65,64,76,117,85", "endOffsets": "3613,1471,1781,1817,1889,1853,1961,1925,1674,1631,165,2890,812,1370,2942,3250,105,2752,2308,859,501,2551,3134,2372,3296,1041,1101,613,2652,232,2809,554,3340,3064,1743,1143,2700,2000,2059,345,3391,1209,393,739,1511,447,300,1430,2995,1314,898,1271,2423,678,2850,1569,3201,2125,2501,2244,985"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,536,576,613,648,683,718,753,788,3516,3577,3636,3675,3747,3802,3853,4138,4191,4940,5003,5287,5340,5470,8050,8208,8253,8308,8434,10166,10668,11152,11208,11398,11441,11509,11577,11618,12665,12703,16509,16553,16603,16750,16797,16857,17026,17161,17228,17287,17440,17482,17520,17581,17631,17695,18562,18619,18685,18750,18827,18945", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,60,58,38,71,54,50,47,52,50,62,45,52,48,68,62,44,54,58,57,99,65,55,51,42,67,67,40,46,37,57,43,49,64,46,59,38,52,66,58,51,41,37,60,49,63,39,56,65,64,76,117,85", "endOffsets": "360,571,608,643,678,713,748,783,825,3572,3631,3670,3742,3797,3848,3896,4186,4237,4998,5044,5335,5384,5534,8108,8248,8303,8362,8487,10261,10729,11203,11255,11436,11504,11572,11613,11660,12698,12756,16548,16598,16663,16792,16852,16891,17074,17223,17282,17334,17477,17515,17576,17626,17690,17730,18614,18680,18745,18822,18940,19026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\305fe414d5006b1315936043a2188629\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6596", "endColumns": "131", "endOffsets": "6723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7938,8863,8971,9083", "endColumns": "111,107,111,111", "endOffsets": "8045,8966,9078,9190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,453,548,701,816,942,1065,1221,1358,1511,1643,1787,1881,2054,2189,2339,2485,2607,2737,2838,2977,3066,3199,3308,3455", "endColumns": "157,125,113,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "208,334,448,543,696,811,937,1060,1216,1353,1506,1638,1782,1876,2049,2184,2334,2480,2602,2732,2833,2972,3061,3194,3303,3450,3559"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4242,4400,7824,8113,8595,8748,9195,9321,9444,9600,9737,9890,10022,10266,10360,10533,10734,10884,11030,16896,17735,17836,17975,18064,18197,18306,18453", "endColumns": "157,125,113,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "4395,4521,7933,8203,8743,8858,9316,9439,9595,9732,9885,10017,10161,10355,10528,10663,10879,11025,11147,17021,17831,17970,18059,18192,18301,18448,18557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f944e6341a47ce5ae5325f3246eaddd\\transformed\\jetified-play-services-base-18.0.1\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5539,5650,5826,5961,6065,6233,6362,6486,6728,6888,6998,7163,7294,7452,7609,7670,7739", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "5645,5821,5956,6060,6228,6357,6481,6591,6883,6993,7158,7289,7447,7604,7665,7734,7819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "17339", "endColumns": "100", "endOffsets": "17435"}}]}]}