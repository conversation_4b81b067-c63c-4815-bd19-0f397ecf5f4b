<?xml version="1.0" encoding="utf-8"?>
<!--<androidx.constraintlayout.widget.ConstraintLayout-->
<!--    xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:id="@+id/root_layout"-->
<!--    android:background="@android:color/white">-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_layout"
    android:background="@android:color/white">

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2"
        tools:ignore="MissingConstraints,UnknownId">

        <!--        <Button-->
<!--            android:id="@+id/profile"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="16dp"-->
<!--            android:background="@drawable/backg_button"-->
<!--            android:gravity="center"-->
<!--            android:padding="16dp"-->
<!--            android:text="@string/user_profile"-->
<!--            android:textColor="#FFFFFF"-->
<!--            android:textSize="16sp"-->
<!--            android:textStyle="bold" />-->



    </LinearLayout>

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="132dp"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/date_picker_button"
                android:background="@drawable/backg_button"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/pick_date"
                android:textColor="#FFFFFF"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/title_date"
                android:layout_width="0dp"
                android:layout_height="47dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/date_x"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Data Rows -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/balance"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/trans_balance"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/_0_00"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/target"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/trans_target"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/_0_00"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/budget"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/trans_budget"
                android:layout_width="0dp"
                android:layout_height="52dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/_0_00"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/set_new_target"
                android:textColor="#111418"
                android:textSize="15sp"
                android:textStyle="bold"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/editText4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/edittext_background"
                android:hint="@string/_0_00"
                android:padding="13dp"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <Button
                android:id="@+id/save_button"
                android:layout_width="143dp"
                android:layout_height="66dp"
                android:layout_weight="1"
                android:background="@drawable/backg_button"
                android:gravity="center"
                android:padding="12dp"
                android:text="@string/set"
                android:textColor="#FFFFFF"
                android:textSize="17sp"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginTop="64dp"
            android:background="@drawable/button_background"
            android:contentDescription="@string/back_description"
            android:src="@drawable/back"
            android:tint="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.123"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="UseAppTint" />

        <TextView
        android:id="@+id/title"
        android:layout_width="185dp"
        android:layout_height="64dp"
        android:layout_marginTop="60dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/settings"
        android:textColor="#111418"
        android:textSize="23sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />
</LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:padding="16dp"
        android:layout_below="@id/linearLayout2">
    <Button
        android:id="@+id/buttonLanguageSettings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/backg_button"
        android:gravity="center"
        android:padding="16dp"
        android:text="@string/language_settings"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold" />

        <Button
        android:id="@+id/buttonClearUserData"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/backg_button"
        android:gravity="center"
        android:padding="16dp"
        android:text="@string/clear_user_data"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold" />

    </LinearLayout>
    <RelativeLayout
        android:id="@+id/spinner_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">

        <include
            layout="@layout/spinner" />
    </RelativeLayout>
</RelativeLayout>
