<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="incidents">
                    <map id="0">
                        <entry
                            name="incidentClass"
                            string="com.example.budgettracker.ExpensesActivity"/>
                        <location id="secondaryLocation"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                            line="175"
                            column="13"
                            startOffset="7638"
                            endLine="175"
                            endColumn="34"
                            endOffset="7659"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/ExpensesActivity.kt"
                            line="173"
                            column="26"
                            startOffset="7593"
                            endLine="173"
                            endColumn="37"
                            endOffset="7604"/>
                    </map>
                    <map id="1">
                        <entry
                            name="incidentClass"
                            string="com.example.budgettracker.TransactionsActivity"/>
                        <location id="secondaryLocation"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                            line="125"
                            column="13"
                            startOffset="4852"
                            endLine="125"
                            endColumn="34"
                            endOffset="4873"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/TransactionsActivity.kt"
                            line="123"
                            column="26"
                            startOffset="4807"
                            endLine="123"
                            endColumn="37"
                            endOffset="4818"/>
                    </map>
            </map>
            <map id="unprotected">
                <entry
                    name="com.example.budgettracker.ExpensesActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.LanguageActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.RegisterActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.SettingsActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.NotificationActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.DashboardActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.TransactionsActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.ProfileActivity"
                    boolean="true"/>
                <entry
                    name="com.example.budgettracker.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/LanguageActivity.kt"
            line="41"
            column="9"
            startOffset="1319"
            endLine="41"
            endColumn="33"
            endOffset="1343"/>
    </map>
    <map id="UnusedResources">
        <location id="R.layout.content_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="18"
            endColumn="53"
            endOffset="894"/>
        <location id="R.string.today"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1227"
            endLine="24"
            endColumn="25"
            endOffset="1239"/>
        <location id="R.drawable.button_ripple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_ripple.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="10"
            endOffset="227"/>
        <location id="R.string.first_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="171"
            endLine="5"
            endColumn="40"
            endOffset="198"/>
        <location id="R.drawable.spinner_normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_normal.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="9"
            endOffset="227"/>
        <location id="R.string.action_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="117"
            endLine="4"
            endColumn="35"
            endOffset="139"/>
        <location id="R.navigation.nav_graph"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/navigation/nav_graph.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="26"
            endColumn="14"
            endOffset="1020"/>
        <location id="R.string.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="342"
            endLine="8"
            endColumn="28"
            endOffset="357"/>
        <location id="R.drawable.work_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="1045"/>
        <location id="R.string.meta"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1880"
            endLine="39"
            endColumn="24"
            endOffset="1891"/>
        <location id="R.drawable.spinner_press"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_press.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="9"
            endOffset="226"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1731"/>
        <location id="R.string._5_943"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1566"
            endLine="31"
            endColumn="26"
            endOffset="1579"/>
        <location id="R.drawable.spinner_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml"
            line="17"
            column="1"
            startOffset="687"
            endLine="23"
            endColumn="12"
            endOffset="1052"/>
        <location id="R.drawable.spinner_select"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_select.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="9"
            endOffset="232"/>
        <location id="R.drawable.settings_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="1717"/>
        <location id="R.array.categories_array"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="83"
            column="19"
            startOffset="4259"
            endLine="83"
            endColumn="42"
            endOffset="4282"/>
        <entry
            name="model"
            string="array[categories_array(D)],attr[colorControlNormal(R),colorControlHighlight(E),actionBarSize(E)],color[black(U),white(U),primary(U),primary_blue(U),radio_dot_color(U),text_color(U),steelblue(U),onPrimary(D),border_color(D),back_ground(D),material_dynamic_primary10(E),material_dynamic_primary20(E),material_dynamic_secondary10(E),material_dynamic_secondary20(E),material_dynamic_secondary30(E),material_dynamic_secondary50(E)],dimen[fab_margin(D)],drawable[attach_money(U),back(U),backg_button(U),button_background(U),button_ripple(D),credit_card(U),edittext_background(U),eye_close(U),eye_icon(U),fingerprint(U),home(U),ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E),logos(U),logout(U),mail_24px(D),monitoring(D),notifications(U),person(U),search(D),settings_24px(D),shopping(U),spinner_background(D),spinner_press(D),spinner_select(D),spinner_normal(D),warning_24px(U),work_24px(D)],id[toolbar(U),fab(D),root_layout(U),editText3(U),date_picker_button(U),textView(U),back_button(U),editText2(U),editText4(U),save_button(U),category_spinner(U),spinner_container(D),title(U),linearLayout3(U),linearLayout(U),budget_info(U),linearLayout5(U),itemNameHeader(D),itemAmountHeader(D),itemDateHeader(D),recyclerView(U),editText5(U),button_home(U),button_expenses(U),button_transactions(U),button_profile(U),nav_host_fragment_content_main(U),linearLayout2(U),firstTextView(D),button_notification(U),secondTextView(U),button_logout(U),footer(U),budget_id(U),month(U),graph(U),itemName(U),radio_group(U),radio_english(U),radio_afrikaans(U),radio_zulu(U),list_icon(D),itemAmount(U),itemDate(U),welcome_icon(U),description(U),email_container(U),email_input(U),password_container(U),password_input(U),password_eye(U),login_button(U),or_login_with(U),fingerprint_icon(U),fingerprint_instead(U),signup_instead(U),button(U),clear_all_button(U),username_container(D),username_input(U),password_container_confrim(D),password_input_confrim(U),password_eye_confrim(U),signup_button(U),title_date(U),trans_balance(U),trans_target(U),trans_budget(U),buttonLanguageSettings(U),buttonClearUserData(U),loading_spinner(U),loading_text(D),action_settings(D),nav_graph(D),FirstFragment(U),action_FirstFragment_to_SecondFragment(R),closeIcon(D),profile_section(D),header_section(D)],layout[activity_main(D),content_main(D),addexpense(U),spinner(U),addtransaction(U),base(U),dashboard(U),language(U),list_bug(U),list_item(U),login(U),notifications(U),register(U),settings(U),spinner_item(D)],menu[menu_main(D)],mipmap[logos(U),ic_launcher(D),ic_launcher_round(D)],navigation[nav_graph(D)],string[app_name(U),todo(U),date_x(U),add_a_new_expense(U),back_description(U),add_a_note(U),_0_00(U),save(U),pick_date(U),add_money(U),name_bug(U),bug_amount(U),type(U),add(U),home_description(U),expense_description(U),transaction_description(U),profile_description(U),logo_name(U),notifications(U),user_name(U),account_balance(U),_0(U),month(U),_12(U),_1d(U),_1w(U),_1m(U),_3m(U),_1y(U),heading_list_dash(U),english(U),afrikaans(U),zulu(U),language(U),exp_date(U),ext_name(U),exp_amount(U),your_personal_budget_tracker(U),enter_email(U),enter_password(U),log_in(U),or_log_in_with(U),fingerprint_icon_description(U),touch_id(U),sign_up(U),warning_icon(U),budget_alert(U),you_ve_spent_70_of_your_budget_for_the_month(U),currency_icon(U),transaction(U),you_ve_received_25_from_sarah(U),clear_all(U),balance(U),target(U),budget(U),set_new_target(U),set(U),settings(U),language_settings(U),clear_user_data(U),action_settings(D),first_fragment_label(D),second_fragment_label(D),next(D),previous(D),search_description(D),chart_description(D),cashmate(D),today(D),select_category(D),_5_943(D),last_30_days(D),meta(D),meta_platforms(D),budget_info(D),user_profile(D),main_cash(D)],style[Base_Theme_BudgetTracker(U),AppTheme(D),Theme_MaterialComponents_Light_NoActionBar(E),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];15^1,16^1,19^2^18,1a^1,1c^1,1d^1,1e^1,1f^1,21^22,24^1,25^1,26^1,27^1,28^1,29^1,2a^1,2b^1,2c^2d^2e^2f,30^1,31^1,81^3^82^96^14,82^94,83^36^97^37^98^18^99^16^4^1b^9a^3a^9b^35^17^39^9c^5^9d^84,84^78,85^18^99^16^4^38^9e^3e^3f^40^9f^a0^97^42^1b^a1^46^9b^47^17^a2^84,86^5^18^a3^1f^4^a4^15^a5^1a^a6^28,87^a7^6^18^a8^27^4^a9^24^52^4d^7^aa^ab^ac^ad^ae^af^b0^b1^b2^b3^56^86^84,88^5^8^b4^9^b5^b6^18^99^16^4^38^b7,89^15^9f^a0^b8,8a^2b^b9^ba^b8,8b^96^23^5e^bb^1b^5f^bc^60^bd^1d^a^62^be^5^65^bf^c0^68^1e^66^c1^67^c2^84,8c^18^99^16^4^6a^a8^6b^32^c3^30^c4^c5^c6^15^c7^c8^7^c9^5,8d^18^99^16^4^38^9^1b^5f^1d^60^a^5^84,8e^4d^17^9d^97^ca^9b^cb^cc^cd^1b^4^ce^18^99^16^cf^d0^d1^84,90^d2,92^20^21,93^20^21,94^7c^d3^8b,e3^e6^5,e4^e5^e^f^10^11^12^13;;;"/>
        <location id="R.string.budget_info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2910"
            endLine="55"
            endColumn="31"
            endOffset="2928"/>
        <location id="R.drawable.monitoring"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="11"
            endColumn="10"
            endOffset="648"/>
        <location id="R.mipmap.ic_launcher"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp"/>
        <location id="R.color.onPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="256"
            endLine="8"
            endColumn="28"
            endOffset="272"/>
        <location id="R.color.back_ground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="494"
            endLine="13"
            endColumn="30"
            endOffset="512"/>
        <location id="R.layout.spinner_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/spinner_item.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="31"
            endOffset="344"/>
        <location id="R.string.cashmate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="793"
            endLine="16"
            endColumn="28"
            endOffset="808"/>
        <location id="R.menu.menu_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="8"
            endOffset="422"/>
        <location id="R.string.last_30_days"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1609"
            endLine="32"
            endColumn="32"
            endOffset="1628"/>
        <location id="R.string.select_category"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1319"
            endLine="26"
            endColumn="35"
            endOffset="1341"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="170"
            endColumn="10"
            endOffset="5774"/>
        <location id="R.color.border_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="395"
            endLine="11"
            endColumn="31"
            endOffset="414"/>
        <location id="R.string.user_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3244"
            endLine="61"
            endColumn="32"
            endOffset="3263"/>
        <location id="R.layout.activity_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="34"
            endColumn="55"
            endOffset="1422"/>
        <location id="R.string.second_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="236"
            endLine="6"
            endColumn="41"
            endOffset="264"/>
        <location id="R.string.chart_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="444"
            endLine="10"
            endColumn="37"
            endOffset="468"/>
        <location id="R.drawable.mail_24px"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="777"/>
        <location id="R.style.AppTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="27"
            endOffset="79"/>
        <location id="R.dimen.fab_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="2"
            column="12"
            startOffset="24"
            endLine="2"
            endColumn="29"
            endOffset="41"/>
        <location id="R.drawable.search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="687"/>
        <location id="R.string.main_cash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="3804"
            endLine="73"
            endColumn="29"
            endOffset="3820"/>
        <location id="R.string.meta_platforms"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2003"
            endLine="41"
            endColumn="34"
            endOffset="2024"/>
        <location id="R.string.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="303"
            endLine="7"
            endColumn="24"
            endOffset="314"/>
        <location id="R.mipmap.ic_launcher_round"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp"/>
        <location id="R.string.search_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="389"
            endLine="9"
            endColumn="38"
            endOffset="414"/>
    </map>

</incidents>
