[{"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/list_item.xml", "source": "com.example.budgettracker.app-main-60:/layout/list_item.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/addtransaction.xml", "source": "com.example.budgettracker.app-main-60:/layout/addtransaction.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/login.xml", "source": "com.example.budgettracker.app-main-60:/layout/login.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/spinner_item.xml", "source": "com.example.budgettracker.app-main-60:/layout/spinner_item.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/spinner.xml", "source": "com.example.budgettracker.app-main-60:/layout/spinner.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/notifications.xml", "source": "com.example.budgettracker.app-main-60:/layout/notifications.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/register.xml", "source": "com.example.budgettracker.app-main-60:/layout/register.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/base.xml", "source": "com.example.budgettracker.app-main-60:/layout/base.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/activity_main.xml", "source": "com.example.budgettracker.app-main-60:/layout/activity_main.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/settings.xml", "source": "com.example.budgettracker.app-main-60:/layout/settings.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/list_bug.xml", "source": "com.example.budgettracker.app-main-60:/layout/list_bug.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/addexpense.xml", "source": "com.example.budgettracker.app-main-60:/layout/addexpense.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/dashboard.xml", "source": "com.example.budgettracker.app-main-60:/layout/dashboard.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/content_main.xml", "source": "com.example.budgettracker.app-main-60:/layout/content_main.xml"}, {"merged": "com.example.budgettracker.app-mergeReleaseResources-58:/layout/language.xml", "source": "com.example.budgettracker.app-main-60:/layout/language.xml"}]