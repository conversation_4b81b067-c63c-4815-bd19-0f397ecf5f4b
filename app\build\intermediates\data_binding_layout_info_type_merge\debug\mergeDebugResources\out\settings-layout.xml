<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="settings" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/settings_0" view="RelativeLayout"><Expressions/><location startLine="9" startOffset="0" endLine="293" endOffset="16"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="283" startOffset="4" endLine="292" endOffset="20"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="290" startOffset="8" endLine="291" endOffset="38"/></Target><Target id="@+id/linearLayout" view="LinearLayout"><Expressions/><location startLine="17" startOffset="4" endLine="42" endOffset="18"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="44" startOffset="4" endLine="211" endOffset="18"/></Target><Target id="@+id/date_picker_button" view="Button"><Expressions/><location startLine="59" startOffset="12" endLine="70" endOffset="42"/></Target><Target id="@+id/title_date" view="TextView"><Expressions/><location startLine="72" startOffset="12" endLine="81" endOffset="42"/></Target><Target id="@+id/trans_balance" view="TextView"><Expressions/><location startLine="99" startOffset="12" endLine="108" endOffset="42"/></Target><Target id="@+id/trans_target" view="TextView"><Expressions/><location startLine="125" startOffset="12" endLine="134" endOffset="42"/></Target><Target id="@+id/trans_budget" view="TextView"><Expressions/><location startLine="151" startOffset="12" endLine="160" endOffset="42"/></Target><Target id="@+id/editText4" view="EditText"><Expressions/><location startLine="185" startOffset="12" endLine="195" endOffset="41"/></Target><Target id="@+id/save_button" view="Button"><Expressions/><location startLine="197" startOffset="12" endLine="208" endOffset="42"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="219" startOffset="8" endLine="232" endOffset="39"/></Target><Target id="@+id/title" view="TextView"><Expressions/><location startLine="234" startOffset="8" endLine="248" endOffset="43"/></Target><Target id="@+id/buttonLanguageSettings" view="Button"><Expressions/><location startLine="257" startOffset="4" endLine="268" endOffset="34"/></Target><Target id="@+id/buttonClearUserData" view="Button"><Expressions/><location startLine="270" startOffset="8" endLine="280" endOffset="34"/></Target></Targets></Layout>