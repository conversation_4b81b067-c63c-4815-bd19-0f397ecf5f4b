{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-57:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,433,545,658,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,14406", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "428,540,653,741,848,974,1052,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2147,2263,2373,2472,2585,2690,2804,2968,3068,14484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3073,3187", "endColumns": "113,113", "endOffsets": "3182,3296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,353,436,518,633,728,835,948,1033,1096,1190,1256,1318,1421,1487,1558,1617,1693,1758,1812,1925,1983,2044,2098,2177,2293,2376,2467,2609,2688,2767,2896,2984,3068,3125,3177,3243,3323,3413,3497,3576,3653,3730,3807,3876,3993,4092,4169,4262,4357,4431,4512,4608,4659,4743,4811,4897,4985,5048,5113,5176,5244,5349,5454,5549,5652,5713,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "264,348,431,513,628,723,830,943,1028,1091,1185,1251,1313,1416,1482,1553,1612,1688,1753,1807,1920,1978,2039,2093,2172,2288,2371,2462,2604,2683,2762,2891,2979,3063,3120,3172,3238,3318,3408,3492,3571,3648,3725,3802,3871,3988,4087,4164,4257,4352,4426,4507,4603,4654,4738,4806,4892,4980,5043,5108,5171,5239,5344,5449,5544,5647,5708,5764,5846"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3577,3661,3744,3826,3941,4036,4143,4256,6888,6951,9366,9432,9494,9597,9663,9734,9793,9869,9934,9988,10101,10159,10220,10274,10353,10469,10552,10643,10785,10864,10943,11072,11160,11244,11301,11353,11419,11499,11589,11673,11752,11829,11906,11983,12052,12169,12268,12345,12438,12533,12607,12688,12784,12835,12919,12987,13073,13161,13224,13289,13352,13420,13525,13630,13725,13828,13889,14192", "endLines": "5,37,38,39,40,41,42,43,44,66,67,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,145", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "314,3656,3739,3821,3936,4031,4138,4251,4336,6946,7040,9427,9489,9592,9658,9729,9788,9864,9929,9983,10096,10154,10215,10269,10348,10464,10547,10638,10780,10859,10938,11067,11155,11239,11296,11348,11414,11494,11584,11668,11747,11824,11901,11978,12047,12164,12263,12340,12433,12528,12602,12683,12779,12830,12914,12982,13068,13156,13219,13284,13347,13415,13520,13625,13720,13823,13884,13940,14269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6686,7300,7400,7516", "endColumns": "113,99,115,100", "endOffsets": "6795,7395,7511,7612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f944e6341a47ce5ae5325f3246eaddd\\transformed\\jetified-play-services-base-18.0.1\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4341,4452,4622,4755,4870,5013,5142,5250,5495,5645,5758,5923,6058,6203,6360,6429,6492", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "4447,4617,4750,4865,5008,5137,5245,5344,5640,5753,5918,6053,6198,6355,6424,6487,6572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "14489", "endColumns": "100", "endOffsets": "14585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,331,440,528,673,783,898,1029,1161,1300,1434,1571,1720,1820,1977,2106,2256,2404,2532,2664,2761,2898,2985,3107,3207,3348", "endColumns": "157,117,108,87,144,109,114,130,131,138,133,136,148,99,156,128,149,147,127,131,96,136,86,121,99,140,98", "endOffsets": "208,326,435,523,668,778,893,1024,1156,1295,1429,1566,1715,1815,1972,2101,2251,2399,2527,2659,2756,2893,2980,3102,3202,3343,3442"}, "to": {"startLines": "35,36,63,65,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,146,149,150,151,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3301,3459,6577,6800,7045,7190,7617,7732,7863,7995,8134,8268,8405,8554,8654,8811,8940,9090,9238,14274,14590,14687,14824,14911,15033,15133,15274", "endColumns": "157,117,108,87,144,109,114,130,131,138,133,136,148,99,156,128,149,147,127,131,96,136,86,121,99,140,98", "endOffsets": "3454,3572,6681,6883,7185,7295,7727,7858,7990,8129,8263,8400,8549,8649,8806,8935,9085,9233,9361,14401,14682,14819,14906,15028,15128,15269,15368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,128", "endOffsets": "168,297"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "13945,14063", "endColumns": "117,128", "endOffsets": "14058,14187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\305fe414d5006b1315936043a2188629\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5349", "endColumns": "145", "endOffsets": "5490"}}]}]}