{"logs": [{"outputFile": "com.example.budgettracker.app-mergeReleaseResources-57:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1055,1143,1209,1272,1358,1420,1481,1539,1605,1668,1723,1841,1898,1960,2015,2084,2203,2291,2374,2513,2596,2677,2805,2892,2969,3027,3078,3144,3213,3289,3375,3451,3525,3604,3677,3748,3851,3938,4009,4098,4188,4260,4335,4422,4473,4552,4619,4700,4784,4846,4910,4973,5043,5147,5250,5346,5446,5508,5563", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "269,350,430,508,603,691,791,905,986,1050,1138,1204,1267,1353,1415,1476,1534,1600,1663,1718,1836,1893,1955,2010,2079,2198,2286,2369,2508,2591,2672,2800,2887,2964,3022,3073,3139,3208,3284,3370,3446,3520,3599,3672,3743,3846,3933,4004,4093,4183,4255,4330,4417,4468,4547,4614,4695,4779,4841,4905,4968,5038,5142,5245,5341,5441,5503,5558,5635"}, "to": {"startLines": "9,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "356,4493,4574,4654,4732,4827,5023,5123,5344,8205,8325,10942,11008,11320,11406,11468,11529,11587,11653,11716,11771,11889,11946,12008,12063,12132,12347,12435,12518,12657,12740,12821,12949,13036,13113,13171,13222,13288,13357,13433,13519,13595,13669,13748,13821,13892,13995,14082,14153,14242,14332,14404,14479,14566,14617,14696,14763,14844,14928,14990,15054,15117,15187,15291,15394,15490,15590,15652,16083", "endLines": "12,61,62,63,64,65,68,69,72,99,101,124,125,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,193", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "525,4569,4649,4727,4822,4910,5118,5232,5420,8264,8408,11003,11066,11401,11463,11524,11582,11648,11711,11766,11884,11941,12003,12058,12127,12246,12430,12513,12652,12735,12816,12944,13031,13108,13166,13217,13283,13352,13428,13514,13590,13664,13743,13816,13887,13990,14077,14148,14237,14327,14399,14474,14561,14612,14691,14758,14839,14923,14985,15049,15112,15182,15286,15389,15485,15585,15647,15702,16155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5fc560d4a9dbb1eb5cb67bff25db2518\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "55,56", "startColumns": "4,4", "startOffsets": "3873,3985", "endColumns": "111,121", "endOffsets": "3980,4102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6f944e6341a47ce5ae5325f3246eaddd\\transformed\\jetified-play-services-base-18.0.1\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5493,5600,5756,5882,5992,6142,6264,6385,6630,6796,6904,7061,7188,7327,7481,7547,7610", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "5595,5751,5877,5987,6137,6259,6380,6482,6791,6899,7056,7183,7322,7476,7542,7605,7684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b7504402ff25b4156870273582a3050\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "93,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "7799,8667,8769,8882", "endColumns": "104,101,112,99", "endOffsets": "7899,8764,8877,8977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0b881c0ea3c286d2d70c39490113a0ee\\transformed\\navigation-ui-2.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,157", "endColumns": "101,116", "endOffsets": "152,269"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "15707,15809", "endColumns": "101,116", "endOffsets": "15804,15921"}}, {"source": "C:\\Users\\<USER>\\Desktop\\BudgetTracker\\app\\src\\main\\res\\values-af\\strings.xml", "from": {"startLines": "62,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3334,1415,1709,1747,1819,1783,1891,1855,1610,1550,114,2805,735,1300,2846,3141,57,2656,2209,805,445,2467,3011,2273,3190,983,1038,556,2521,171,2708,500,3234,2948,1653,1099,2609,1927,1966,304,3277,1144,347,678,1456,392,237,1355,2898,1258,851,1204,2327,613,2766,1496,3080,2025,2379,2085,890", "endLines": "68,26,32,33,35,34,37,36,30,29,3,52,14,24,53,58,2,49,42,15,9,46,56,43,59,18,19,11,47,4,50,10,60,55,31,20,48,38,39,6,61,21,7,13,27,8,5,25,54,23,16,22,44,12,51,28,57,40,45,41,17", "endColumns": "19,39,36,34,34,34,34,34,41,58,55,39,68,53,50,47,55,50,62,44,53,52,67,52,42,53,59,55,86,64,56,54,41,61,54,43,45,37,57,41,55,58,43,55,38,51,65,58,48,40,37,52,50,63,37,52,59,58,86,122,91", "endOffsets": "3541,1450,1741,1777,1849,1813,1921,1885,1647,1604,165,2840,799,1349,2892,3184,108,2702,2267,845,494,2515,3074,2321,3228,1032,1093,607,2603,231,2760,550,3271,3005,1703,1138,2650,1960,2019,341,3328,1198,386,729,1490,439,298,1409,2942,1294,884,1252,2373,672,2799,1544,3135,2079,2461,2203,977"}, "to": {"startLines": "2,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,530,570,607,642,677,712,747,782,3496,3555,3611,3651,3720,3774,3825,4107,4163,4915,4978,5237,5291,5425,7904,8048,8091,8145,8269,9891,10360,10830,10887,11071,11113,11175,11230,11274,12251,12289,15926,15968,16024,16160,16204,16260,16423,16556,16622,16681,16831,16872,16910,16963,17014,17078,17870,17923,17983,18042,18129,18252", "endLines": "8,13,14,15,16,17,18,19,20,48,49,50,51,52,53,54,57,58,66,67,70,71,73,94,96,97,98,100,114,118,122,123,126,127,128,129,130,144,145,190,191,192,194,195,196,198,200,201,202,204,205,206,207,208,209,217,218,219,220,221,222", "endColumns": "19,39,36,34,34,34,34,34,41,58,55,39,68,53,50,47,55,50,62,44,53,52,67,52,42,53,59,55,86,64,56,54,41,61,54,43,45,37,57,41,55,58,43,55,38,51,65,58,48,40,37,52,50,63,37,52,59,58,86,122,91", "endOffsets": "351,565,602,637,672,707,742,777,819,3550,3606,3646,3715,3769,3820,3868,4158,4209,4973,5018,5286,5339,5488,7952,8086,8140,8200,8320,9973,10420,10882,10937,11108,11170,11225,11269,11315,12284,12342,15963,16019,16078,16199,16255,16294,16470,16617,16676,16725,16867,16905,16958,17009,17073,17111,17918,17978,18037,18124,18247,18339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d4c67547e624b6cccfef70d7d267bfc\\transformed\\biometric-1.2.0-alpha05\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,334,444,535,676,789,901,1019,1157,1300,1421,1554,1698,1798,1953,2080,2218,2363,2485,2609,2705,2829,2916,3035,3136,3265", "endColumns": "156,121,109,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "207,329,439,530,671,784,896,1014,1152,1295,1416,1549,1693,1793,1948,2075,2213,2358,2480,2604,2700,2824,2911,3030,3131,3260,3358"}, "to": {"startLines": "59,60,92,95,102,103,107,108,109,110,111,112,113,115,116,117,119,120,121,197,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4214,4371,7689,7957,8413,8554,8982,9094,9212,9350,9493,9614,9747,9978,10078,10233,10425,10563,10708,16299,17116,17212,17336,17423,17542,17643,17772", "endColumns": "156,121,109,90,140,112,111,117,137,142,120,132,143,99,154,126,137,144,121,123,95,123,86,118,100,128,97", "endOffsets": "4366,4488,7794,8043,8549,8662,9089,9207,9345,9488,9609,9742,9886,10073,10228,10355,10558,10703,10825,16418,17207,17331,17418,17537,17638,17767,17865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\41c0f31cef78f5453d5fb29f83ccc123\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "203", "startColumns": "4", "startOffsets": "16730", "endColumns": "100", "endOffsets": "16826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\305fe414d5006b1315936043a2188629\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "6487", "endColumns": "142", "endOffsets": "6625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,932,1028,1134,1219,1322,1440,1517,1593,1684,1777,1872,1966,2065,2158,2253,2352,2447,2541,2622,2729,2834,2931,3039,3142,3244,3398,16475", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "927,1023,1129,1214,1317,1435,1512,1588,1679,1772,1867,1961,2060,2153,2248,2347,2442,2536,2617,2724,2829,2926,3034,3137,3239,3393,3491,16551"}}]}]}