package com.example.budgettracker

import android.annotation.SuppressLint
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.widget.ImageButton
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.appcompat.app.AppCompatActivity
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*

class NotificationActivity : AppCompatActivity() {
    
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var apiService: ApiService
    private var user_id: String? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid()
        }
        
        setContentView(R.layout.notifications)
        dbHelper = DatabaseHelper(this)
        
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDate = sdf.format(Date())
        val monthYear = currentDate.substring(0, 7)

        if (NetworkUtils.isInternetAvailable(this)) {
            checkBudgetStatusAndNotifyOnline(monthYear)
        } else {
            checkBudgetStatusAndNotify(monthYear)
        }

        apiService = RetrofitClient.getClient().create(ApiService::class.java)
        NotificationHelper.createNotificationChannel(this)
        
        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@NotificationActivity, DashboardActivity::class.java)
            startActivity(intent)
        }
    }

    private fun checkBudgetStatusAndNotify(monthYear: String) {
        val db = dbHelper.readableDatabase
        val query = "SELECT trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id.toString()))
        
        if (cursor.moveToFirst()) {
            @SuppressLint("Range")
            val balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"))
            @SuppressLint("Range")
            val target = cursor.getDouble(cursor.getColumnIndex("trans_target"))
            
            when {
                balance > target -> {
                    NotificationHelper.sendNotification(this, "Budget Alert", "You have exceeded your budget!")
                }
                balance == target -> {
                    NotificationHelper.sendNotification(this, "Budget Achievement", "Congratulations! You have achieved your budget target.")
                }
            }
        }
        cursor.close()
        db.close()
    }

    private fun checkBudgetStatusAndNotifyOnline(monthYear: String) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("transactions", monthYear, user_id.toString())
        
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!

                    for (record in records) {
                        if (record is Transaction) {
                            val balance = record.getBalance()
                            val target = record.getTransTarget()
                            val budget = record.getTotal()

                            when {
                                balance >= target -> {
                                    NotificationHelper.sendNotification(this@NotificationActivity, "Budget Alert", "You have exceeded your Target!")
                                }
                                balance == target -> {
                                    NotificationHelper.sendNotification(this@NotificationActivity, "Budget Achievement", "Congratulations! You have achieved your budget target.")
                                }
                                balance < target -> {
                                    NotificationHelper.sendNotification(this@NotificationActivity, "Budget Achievement", "Congratulations! You have achieved (more) your budget target.")
                                }
                                balance > budget -> {
                                    NotificationHelper.sendNotification(this@NotificationActivity, "Budget Alert", "You have exceeded your budget!")
                                }
                            }
                            break
                        }
                    }
                } else {
                    Toast.makeText(this@NotificationActivity, "Error with notification", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                Toast.makeText(this@NotificationActivity, "Error with notification", Toast.LENGTH_SHORT).show()
            }
        })
    }
}
