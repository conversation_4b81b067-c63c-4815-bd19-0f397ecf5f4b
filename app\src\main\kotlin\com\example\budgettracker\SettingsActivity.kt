package com.example.budgettracker

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.ContentValues
import android.content.Intent
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class SettingsActivity : AppCompatActivity() {
    
    private lateinit var dateTextView: TextView
    private lateinit var balanceTextView: TextView
    private lateinit var targetTextView: TextView
    private lateinit var budgetTextView: TextView
    private lateinit var dbHelper: DatabaseHelper
    private lateinit var editText4: EditText
    private lateinit var database: SQLiteDatabase
    private lateinit var calendar: Calendar
    private lateinit var dateFormat: SimpleDateFormat
    private var user_id: String? = null
    private lateinit var loadingSpinner: LoadingSpinner

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.settings)
        
        val rootLayout = findViewById<RelativeLayout>(R.id.root_layout)
        loadingSpinner = LoadingSpinner(this, rootLayout)
        
        val sessionManager = SessionManager(this)
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid()
        }
        
        dbHelper = DatabaseHelper(this)
        editText4 = findViewById(R.id.editText4)
        val saveButton = findViewById<Button>(R.id.save_button)
        val datePickerButton = findViewById<Button>(R.id.date_picker_button)
        val clearDataButton = findViewById<Button>(R.id.buttonClearUserData)
        dateTextView = findViewById(R.id.title_date)
        balanceTextView = findViewById(R.id.trans_balance)
        targetTextView = findViewById(R.id.trans_target)
        budgetTextView = findViewById(R.id.trans_budget)
        @SuppressLint("CutPasteId") 
        val languageSettingsButton = findViewById<Button>(R.id.buttonLanguageSettings)
        @SuppressLint("CutPasteId") 
        val clearUserDataButton = findViewById<Button>(R.id.buttonClearUserData)

        calendar = Calendar.getInstance()
        dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        updateDateLabel()
        
        if (NetworkUtils.isInternetAvailable(this)) {
            fetchDataForDateOnline(dateFormat.format(calendar.time))
        } else {
            fetchDataForDate(dateFormat.format(calendar.time))
        }

        datePickerButton.setOnClickListener { showDatePickerDialog() }
        languageSettingsButton.setOnClickListener { 
            val intent = Intent(this@SettingsActivity, LanguageActivity::class.java)
            startActivity(intent)
        }

        val backButton = findViewById<ImageButton>(R.id.back_button)
        backButton.setOnClickListener {
            val intent = Intent(this@SettingsActivity, DashboardActivity::class.java)
            startActivity(intent)
        }

        saveButton.setOnClickListener {
            handleSaveAction()
        }
        
        editText4.setOnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
                v.onKeyDown(keyCode, event)
                handleSaveAction()
                true
            } else {
                false
            }
        }
    }

    private fun showDatePickerDialog() {
        DatePickerDialog(
            this@SettingsActivity,
            { _, year, month, dayOfMonth ->
                calendar.set(year, month, dayOfMonth)
                updateDateLabel()

                if (NetworkUtils.isInternetAvailable(this)) {
                    fetchDataForDateOnline(dateFormat.format(calendar.time))
                } else {
                    fetchDataForDate(dateFormat.format(calendar.time))
                }
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun fetchDataForDate(date: String) {
        val db = dbHelper.readableDatabase
        var cursor: Cursor? = null
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("yyyy-MM", Locale.getDefault())
            val parsedDate = inputFormat.parse(date)
            val monthYear = outputFormat.format(parsedDate!!)

            cursor = db.rawQuery(
                "SELECT trans_balance, trans_target, trans_budget FROM transactions WHERE strftime('%Y-%m', trans_month) = ? AND user_id = ?",
                arrayOf(monthYear, user_id.toString())
            )
            if (cursor.moveToFirst()) {
                balanceTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(0))
                targetTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(1))
                budgetTextView.text = String.format(Locale.getDefault(), "%.2f", cursor.getDouble(2))
            } else {
                balanceTextView.text = "0.00"
                targetTextView.text = "0.00"
                budgetTextView.text = "0.00"
            }
        } catch (e: ParseException) {
            Log.w("DatabaseHelper", "Error parsing date: ${e.message}")
        } finally {
            cursor?.close()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun fetchDataForDateOnline(date: String) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val outputFormat = SimpleDateFormat("yyyy-MM", Locale.getDefault())
        
        try {
            val parsedDate = inputFormat.parse(date)
            if (parsedDate != null) {
                val monthYear = outputFormat.format(parsedDate)
                val call = apiService.getRecords("transactions", monthYear, user_id.toString())
                call.enqueue(object : Callback<List<Record>> {
                    override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                        if (response.isSuccessful && response.body() != null) {
                            val records = response.body()!!
                            var balance = 0.0
                            var target = 0.0
                            var budget = 0.0

                            for (record in records) {
                                if (record is Transaction) {
                                    balance = record.getBalance()
                                    target = record.getTransTarget()
                                    budget = record.getTotal()
                                    break
                                }
                            }
                            balanceTextView.text = String.format(Locale.getDefault(), "%.2f", balance)
                            targetTextView.text = String.format(Locale.getDefault(), "%.2f", target)
                            budgetTextView.text = String.format(Locale.getDefault(), "%.2f", budget)
                            loadingSpinner.hide()
                        } else {
                            balanceTextView.text = "0.00"
                            targetTextView.text = "0.00"
                            budgetTextView.text = "0.00"
                            loadingSpinner.hide()
                            Toast.makeText(this@SettingsActivity, "No data found", Toast.LENGTH_SHORT).show()
                        }
                    }

                    override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                        loadingSpinner.hide()
                        Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                    }
                })
            }
        } catch (e: ParseException) {
            loadingSpinner.hide()
            balanceTextView.text = "0.00"
            targetTextView.text = "0.00"
            budgetTextView.text = "0.00"
        }
    }

    override fun onKeyDown(keyCode: Int, @NonNull event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            startActivity(Intent(this@SettingsActivity, MainActivity::class.java))
            finish()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun updateDateLabel() {
        dateTextView.text = dateFormat.format(calendar.time)
    }

    private fun handleSaveAction() {
        val targetStr = editText4.text.toString()
        
        if (targetStr.isEmpty()) {
            Toast.makeText(this@SettingsActivity, "Please enter a target amount", Toast.LENGTH_SHORT).show()
            return
        }
        
        val targetAmount = try {
            targetStr.toInt()
        } catch (e: NumberFormatException) {
            Toast.makeText(this@SettingsActivity, "Invalid target amount. Please enter a valid number.", Toast.LENGTH_SHORT).show()
            return
        }

        val monthYear = SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(calendar.time)
        if (NetworkUtils.isInternetAvailable(this)) {
            checkExistingOnline(monthYear, targetAmount)
        } else {
            checkExistingTarget(monthYear, targetAmount)
        }
    }

    private fun checkExistingTarget(monthYear: String, newTargetAmount: Int) {
        val db = dbHelper.readableDatabase
        val query = "SELECT COUNT(*) FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?"
        val cursor = db.rawQuery(query, arrayOf(monthYear, user_id.toString()))
        
        if (cursor.moveToFirst()) {
            val count = cursor.getInt(0)
            cursor.close()
            if (count > 0) {
                AlertDialog.Builder(this)
                    .setTitle("Confirmation")
                    .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                    .setPositiveButton("Yes") { _, _ -> updateTarget(monthYear, newTargetAmount) }
                    .setNegativeButton("No", null)
                    .show()
            } else {
                updateTarget(monthYear, newTargetAmount)
            }
        }
    }

    private fun checkExistingOnline(monthYear: String, newTargetAmount: Int) {
        loadingSpinner.show()
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val call = apiService.getRecords("transactions", monthYear, user_id.toString())
        call.enqueue(object : Callback<List<Record>> {
            override fun onResponse(@NonNull call: Call<List<Record>>, @NonNull response: Response<List<Record>>) {
                if (response.isSuccessful && response.body() != null) {
                    val records = response.body()!!
                    var targetExists = false

                    for (record in records) {
                        if (record is Transaction) {
                            targetExists = true
                            break
                        }
                    }

                    if (targetExists) {
                        AlertDialog.Builder(this@SettingsActivity)
                            .setTitle("Confirmation")
                            .setMessage("A target for this month already exists. Do you want to remove the existing target and add the new one?")
                            .setPositiveButton("Yes") { _, _ -> updateTargetOnline(monthYear, newTargetAmount) }
                            .setNegativeButton("No") { _, _ -> loadingSpinner.hide() }
                            .show()
                    } else {
                        updateTargetOnline(monthYear, newTargetAmount)
                    }
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Failed to check existing target", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<List<Record>>, @NonNull t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun updateTarget(monthYear: String, newTargetAmount: Int) {
        val db = dbHelper.writableDatabase
        val values = ContentValues().apply {
            put("trans_target", newTargetAmount)
        }
        
        val rowsAffected = db.update(
            "transactions",
            values,
            "strftime('%Y-%m', trans_month) = ? AND user_id = ?",
            arrayOf(monthYear, user_id.toString())
        )
        
        if (rowsAffected > 0) {
            Toast.makeText(this, "Target updated successfully", Toast.LENGTH_SHORT).show()
        } else {
            insertNewTarget(monthYear, newTargetAmount)
        }
        db.close()
    }

    private fun insertNewTarget(monthYear: String, newTargetAmount: Int) {
        val db = dbHelper.writableDatabase
        val values = ContentValues().apply {
            put("trans_target", newTargetAmount)
            put("trans_month", monthYear)
            put("user_id", user_id)
        }
        
        val newRowId = db.insert("transactions", null, values)
        if (newRowId != -1L) {
            Toast.makeText(this, "New target added successfully", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "Failed to add new target", Toast.LENGTH_SHORT).show()
        }
        db.close()
    }

    private fun updateTargetOnline(monthYear: String, newTargetAmount: Int) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val data = hashMapOf<String, Any>(
            "trans_target" to newTargetAmount,
            "user_id" to user_id!!
        )
        
        val updateCall = apiService.updateRecord("transactions", user_id!!, monthYear, data)
        updateCall.enqueue(object : Callback<Void> {
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                if (response.isSuccessful) {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Target updated successfully", Toast.LENGTH_SHORT).show()
                } else if (response.code() == 404) {
                    insertNewTargetOnline(monthYear, newTargetAmount)
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Failed to update target", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun insertNewTargetOnline(monthYear: String, newTargetAmount: Int) {
        val apiService = RetrofitClient.getClient().create(ApiService::class.java)
        val data = hashMapOf<String, Any>(
            "trans_target" to newTargetAmount,
            "trans_month" to monthYear,
            "user_id" to user_id!!
        )
        
        val insertCall = apiService.createRecord("transactions", data)
        insertCall.enqueue(object : Callback<Void> {
            override fun onResponse(@NonNull call: Call<Void>, @NonNull response: Response<Void>) {
                if (response.isSuccessful) {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "New target added successfully", Toast.LENGTH_SHORT).show()
                } else {
                    loadingSpinner.hide()
                    Toast.makeText(this@SettingsActivity, "Failed to add new target", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(@NonNull call: Call<Void>, @NonNull t: Throwable) {
                loadingSpinner.hide()
                Toast.makeText(this@SettingsActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
}
