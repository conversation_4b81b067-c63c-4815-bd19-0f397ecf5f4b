<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <ImageView
        android:id="@+id/list_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/attach_money" />
    <TextView
        android:id="@+id/itemName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/name_bug"
        android:textColor="#111418"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/itemAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/bug_amount"
        android:textColor="#111418"
        android:textStyle="bold"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/itemDate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/exp_date"
        android:textSize="16sp"
        android:textColor="#111418"
        android:textStyle="bold" />
<!--    <TextView-->
<!--        android:id="@+id/itemName"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_weight="1"-->
<!--        android:text="@string/name_bug"-->
<!--        android:textColor="#111418"-->
<!--        android:textSize="16sp"-->
<!--        android:textStyle="bold" />-->

<!--    <TextView-->
<!--        android:id="@+id/itemAmount"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="@string/bug_amount"-->
<!--        android:textColor="#111418"-->
<!--        android:textStyle="bold"-->
<!--        android:textSize="16sp" />-->
<!--    <TextView-->
<!--        android:id="@+id/itemDate"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="@string/exp_date"-->
<!--        android:textSize="16sp"-->
<!--        android:textColor="#111418"-->
<!--        android:textStyle="bold" />-->

</LinearLayout>