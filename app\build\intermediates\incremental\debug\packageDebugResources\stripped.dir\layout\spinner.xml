<?xml version="1.0" encoding="utf-8"?>
<!--<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="wrap_content"-->
<!--    android:layout_height="wrap_content"-->
<!--    android:padding="16dp">-->

<!--    &lt;!&ndash; Spinning wheel ProgressBar &ndash;&gt;-->
<!--    <ProgressBar-->
<!--        android:id="@+id/loading_spinner"-->
<!--        android:layout_width="60dp"-->
<!--        android:layout_height="60dp"-->
<!--        android:indeterminate="true"-->
<!--        android:layout_centerInParent="true" />-->
<!--    <TextView-->
<!--        android:id="@+id/loading_text"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Loading..."-->
<!--        android:layout_centerHorizontal="true"-->
<!--        android:layout_below="@id/loading_spinner"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:textSize="16sp"-->
<!--        android:textColor="@android:color/black"-->
<!--        tools:ignore="HardcodedText" />-->
<!--</RelativeLayout>-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">

    <!-- Spinning wheel ProgressBar -->
    <ProgressBar
        android:id="@+id/loading_spinner"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:indeterminate="true"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true" />

    <TextView
        android:id="@+id/loading_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Loading..."
        android:layout_centerHorizontal="true"
        android:layout_below="@id/loading_spinner"
        android:layout_marginTop="8dp"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        tools:ignore="HardcodedText" />
</RelativeLayout>
