package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import java.text.SimpleDateFormat;
import java.util.*;

import retrofit2.Call;


public class NotificationActivity extends AppCompatActivity {
        private DatabaseHelper dbHelper;
        private ApiService apiService;
        public String user_id;
        @Override
        protected void onCreate(Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);
            SessionManager sessionManager = new SessionManager(this);
            if (sessionManager.isLoggedIn()) {user_id = sessionManager.getUserUid();}
            setContentView(R.layout.notifications);
            dbHelper = new DatabaseHelper(this);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            String currentDate = sdf.format(new Date());
            String monthYear = currentDate.substring(0, 7);

            if (NetworkUtils.isInternetAvailable(this)) {
                checkBudgetStatusAndNotifyOnline(monthYear);
            }else{
                checkBudgetStatusAndNotify(monthYear);
            }


            apiService = RetrofitClient.getClient().create(ApiService.class);
            NotificationHelper.createNotificationChannel(this);
            ImageButton backButton = findViewById(R.id.back_button);
            backButton.setOnClickListener(v -> {
                Intent intent = new Intent(NotificationActivity.this, DashboardActivity.class);
                startActivity(intent);
            });
        }
    private void checkBudgetStatusAndNotify(String monthYear) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String query = "SELECT trans_balance, trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});
        if (cursor.moveToFirst()) {
            @SuppressLint("Range") double balance = cursor.getDouble(cursor.getColumnIndex("trans_balance"));
            @SuppressLint("Range") double target = cursor.getDouble(cursor.getColumnIndex("trans_target"));
            if (balance > target) {NotificationHelper.sendNotification(this, "Budget Alert", "You have exceeded your budget!");
            } else if (balance == target) {NotificationHelper.sendNotification(this, "Budget Achievement", "Congratulations! You have achieved your budget target.");}}
        cursor.close();
        db.close();
    }
    private void checkBudgetStatusAndNotifyOnline(String monthYear) {
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        Call<List<Record>> call = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
        call.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();


                    for (Record record : records) {
                        if (record instanceof Transaction) {
                            Transaction transaction = (Transaction) record;
                            double balance = transaction.getBalance();
                            double target  = transaction.getTransTarget();
                            double budget = transaction.getTotal();

                            if (balance >= target) {
                                NotificationHelper.sendNotification(NotificationActivity.this, "Budget Alert", "You have exceeded your Target!");
                            } else if (balance == target) {
                                NotificationHelper.sendNotification(NotificationActivity.this, "Budget Achievement", "Congratulations! You have achieved your budget target.");
                            }else if (balance < target) {
                                NotificationHelper.sendNotification(NotificationActivity.this, "Budget Achievement", "Congratulations! You have achieved (more) your budget target.");
                            }else if (balance > budget) {
                                NotificationHelper.sendNotification(NotificationActivity.this, "Budget Alert", "You have exceeded your budget!");
                            }
                            break;
                        }

                    }
                } else {
                    Toast.makeText(NotificationActivity.this, "Error with notification", Toast.LENGTH_SHORT).show();
                }
            }
            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                Toast.makeText(NotificationActivity.this, "Error with notification", Toast.LENGTH_SHORT).show();
            }
        });
    }

}

