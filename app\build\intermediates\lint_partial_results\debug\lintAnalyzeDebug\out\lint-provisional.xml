<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="conditional_incidents">

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1146"
                endOffset="1166"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="22"
            column="9"
            startOffset="1146"
            endLine="22"
            endColumn="29"
            endOffset="1166"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/attach_money.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/attach_money.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="7"
            column="5"
            startOffset="237"
            endLine="7"
            endColumn="25"
            endOffset="257"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="9"
            column="26"
            startOffset="301"
            endLine="9"
            endColumn="46"
            endOffset="321"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/credit_card.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/credit_card.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_close.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_icon.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/eye_icon.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logout.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logout.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="7"
            column="5"
            startOffset="237"
            endLine="7"
            endColumn="25"
            endOffset="257"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/monitoring.xml"
            line="9"
            column="26"
            startOffset="301"
            endLine="9"
            endColumn="46"
            endOffset="321"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/notifications.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/notifications.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/person.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/settings_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/shopping.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/shopping.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/warning_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/warning_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="6"
            column="19"
            startOffset="206"
            endLine="6"
            endColumn="43"
            endOffset="230"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/work_24px.xml"
            line="8"
            column="26"
            startOffset="268"
            endLine="8"
            endColumn="46"
            endOffset="288"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/NotificationHelper.kt"
            line="41"
            column="13"
            startOffset="1720"
            endLine="41"
            endColumn="59"
            endOffset="1766"/>
    </incident>

    <incident
        id="GestureBackNavigation"
        severity="warning"
        message="If intercepting back events, this should be handled through the registration of callbacks on the window level; Please see https://developer.android.com/about/versions/13/features/predictive-back-gesture">
        <show-url
            description="Show https://developer.android.com/about/versions/13/features/predictive-back-gesture"
            url="https://developer.android.com/about/versions/13/features/predictive-back-gesture"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*2}/com/example/budgettracker/SettingsActivity.kt"
            line="210"
            column="24"
            startOffset="8859"
            endLine="210"
            endColumn="45"
            endOffset="8880"/>
    </incident>

</incidents>
