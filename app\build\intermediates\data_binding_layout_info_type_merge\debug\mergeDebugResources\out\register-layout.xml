<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="register" modulePackage="com.example.budgettracker" filePath="app\src\main\res\layout\register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_layout"><Targets><Target id="@+id/root_layout" tag="layout/register_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="3" endLine="194" endOffset="19"/></Target><Target id="@+id/spinner_container" tag="binding_1" view="RelativeLayout"><Expressions/><location startLine="184" startOffset="8" endLine="193" endOffset="24"/></Target><Target tag="binding_1" include="spinner"><Expressions/><location startLine="191" startOffset="12" endLine="192" endOffset="42"/></Target><Target id="@+id/back_button" view="ImageButton"><Expressions/><location startLine="20" startOffset="12" endLine="33" endOffset="43"/></Target><Target id="@+id/username_container" view="LinearLayout"><Expressions/><location startLine="59" startOffset="11" endLine="80" endOffset="25"/></Target><Target id="@+id/username_input" view="EditText"><Expressions/><location startLine="70" startOffset="15" endLine="79" endOffset="49"/></Target><Target id="@+id/email_container" view="LinearLayout"><Expressions/><location startLine="82" startOffset="11" endLine="103" endOffset="25"/></Target><Target id="@+id/email_input" view="EditText"><Expressions/><location startLine="93" startOffset="15" endLine="102" endOffset="49"/></Target><Target id="@+id/password_container" view="LinearLayout"><Expressions/><location startLine="106" startOffset="11" endLine="135" endOffset="25"/></Target><Target id="@+id/password_input" view="EditText"><Expressions/><location startLine="116" startOffset="15" endLine="127" endOffset="49"/></Target><Target id="@+id/password_eye" view="ImageView"><Expressions/><location startLine="129" startOffset="15" endLine="134" endOffset="53"/></Target><Target id="@+id/password_container_confrim" view="LinearLayout"><Expressions/><location startLine="137" startOffset="11" endLine="168" endOffset="25"/></Target><Target id="@+id/password_input_confrim" view="EditText"><Expressions/><location startLine="149" startOffset="15" endLine="160" endOffset="49"/></Target><Target id="@+id/password_eye_confrim" view="ImageView"><Expressions/><location startLine="162" startOffset="15" endLine="167" endOffset="53"/></Target><Target id="@+id/signup_button" view="Button"><Expressions/><location startLine="170" startOffset="11" endLine="182" endOffset="45"/></Target></Targets></Layout>