<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">
    <ImageView
        android:id="@+id/list_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/shopping"
        />
    <TextView
        android:id="@+id/itemName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/ext_name"
        android:textSize="16sp"
        android:textColor="#111418"
        android:textStyle="bold"/>

    <TextView
        android:id="@+id/itemAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/exp_amount"
        android:textSize="16sp"
        android:textColor="#111418"
        android:textStyle="bold" />
    <TextView
        android:id="@+id/itemDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="100dp"
        android:text="@string/exp_date"
        android:textSize="16sp"
        android:textColor="#111418"
        android:textStyle="bold" />
</LinearLayout>