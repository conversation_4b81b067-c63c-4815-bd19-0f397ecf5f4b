package com.example.budgettracker

import com.google.gson.annotations.SerializedName

class User(
    user_id: String,
    var created_date: String,
    var user_name: String,
    user_status: Int,
    var user_email: String
) : Record(user_id) {
    
    var user_status: String = user_status.toString()

    // Get<PERSON> and Setters
    override fun getuser_id(): String = super.getuser_id()

    fun setUserId(user_id: String) {
        super.setuser_id(user_id)
    }

    fun getCreatedDate(): String = created_date

    fun setCreatedDate(created_date: String) {
        this.created_date = created_date
    }

    fun getUserName(): String = user_name

    fun setUserName(user_name: String) {
        this.user_name = user_name
    }

    fun getUserStatus(): String = user_status

    fun setUserStatus(user_status: String) {
        this.user_status = user_status
    }

    fun getUserEmail(): String = user_email

    fun setUserEmail(user_email: String) {
        this.user_email = user_email
    }
}
