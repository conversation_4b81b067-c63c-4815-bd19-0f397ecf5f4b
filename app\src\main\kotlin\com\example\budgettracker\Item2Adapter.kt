package com.example.budgettracker

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.recyclerview.widget.RecyclerView

class Item2Adapter(private val budgetsList: List<Budgets>) : RecyclerView.Adapter<Item2Adapter.ViewHolder>() {

    @NonNull
    override fun onCreateViewHolder(@NonNull parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.list_bug, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(@NonNull holder: ViewHolder, position: Int) {
        val budget = budgetsList[position]
        holder.nameTextView.text = budget.getName()
        holder.dateTextView.text = budget.getDate()
        holder.amountTextView.text = budget.getAmount().toString()
    }

    override fun getItemCount(): Int {
        return budgetsList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val nameTextView: TextView = itemView.findViewById(R.id.itemName)
        val dateTextView: TextView = itemView.findViewById(R.id.itemDate)
        val amountTextView: TextView = itemView.findViewById(R.id.itemAmount)
    }
}
