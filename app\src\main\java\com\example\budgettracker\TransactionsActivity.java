package com.example.budgettracker;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.util.*;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Response;

public class TransactionsActivity extends AppCompatActivity {

    private ApiService apiService;
    private LinearLayout listContainer;
    private RecyclerView recyclerView;
    private DatabaseHelper dbHelper;
    private TextView budgetInfoTextView;
    private int  totalBudgetForCurrentMonth=0;
    public String user_id;
    private LoadingSpinner loadingSpinner;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.addtransaction);
        RelativeLayout rootLayout = findViewById(R.id.root_layout);


        loadingSpinner = new LoadingSpinner(this,rootLayout);

        SessionManager sessionManager = new SessionManager(this);
        if (sessionManager.isLoggedIn()) {
            user_id = sessionManager.getUserUid();
        }

        dbHelper = new DatabaseHelper(this);
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        if (NetworkUtils.isInternetAvailable(this)) {
            fetchAndSetOnline();
            getTotalBudgetForCurrentOnline();
            }else {
            fetchAndSetData();
        }
        budgetInfoTextView = findViewById(R.id.budget_info);


        Button saveButton = findViewById(R.id.save_button);
        EditText editText5 = findViewById(R.id.editText5);
        EditText editText4 = findViewById(R.id.editText4);

        ImageButton backButton = findViewById(R.id.back_button);
        backButton.setOnClickListener(v -> {
            Intent intent = new Intent(TransactionsActivity.this, DashboardActivity.class);
            startActivity(intent);
        });
        editText4.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });

        editText5.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                saveButton.performClick();
                return true;
            }
            return false;
        });
        saveButton.setOnClickListener(v -> {

            String note = editText5.getText().toString();
            String amountString = editText4.getText().toString();
            if (note.isEmpty() || amountString.isEmpty()) {
                Toast.makeText(TransactionsActivity.this, "Please fill in all fields", Toast.LENGTH_SHORT).show();
                return;}

            int budgetAmount;
            try {budgetAmount = Integer.parseInt(amountString);
            } catch (NumberFormatException e) {
                Toast.makeText(TransactionsActivity.this, "Invalid amount. Please enter a valid number.", Toast.LENGTH_SHORT).show();
                return;}
            if (NetworkUtils.isInternetAvailable(this)) {saveDataToOnline(note, budgetAmount);} else{
                saveDataToDatabase(note, budgetAmount);}

        });
    }
        private List<Budgets> parseItems(String json) {
            Gson gson = new Gson();
            Type BudgetsListType = new TypeToken<List<Budgets>>() {}.getType();
            return gson.fromJson(json, BudgetsListType);
        }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToDatabase(String name, int budgetAmount) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear =currentDate.substring(0, 7);
        String query = "SELECT trans_target FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});
        if (cursor != null && cursor.moveToFirst()) {
            @SuppressLint("Range") String transTarget = cursor.getString(cursor.getColumnIndex("trans_target"));
            if (transTarget == null || transTarget.trim().isEmpty()) {
                Toast.makeText(TransactionsActivity.this, "Monthly Target not Set", Toast.LENGTH_SHORT).show();
                cursor.close();
                db.close();
                return;
            }
        }else{
            Toast.makeText(TransactionsActivity.this, "Monthly Target not Set", Toast.LENGTH_SHORT).show();
            return;}



        values.put("bud_name", name);
        values.put("bud_date", currentDate);
        values.put("bud_amount", budgetAmount);
        values.put("user_id", user_id);

        long newRowId = db.insert("budget", null, values);
        if (newRowId == -1) {
            Toast.makeText(TransactionsActivity.this, "Error saving amount", Toast.LENGTH_SHORT).show();
        } else {
            if (NetworkUtils.isInternetAvailable(this)) {updateTransactionOnline(monthYear, budgetAmount);}else{updateTransactionAmounts(db, monthYear, budgetAmount);}
            Toast.makeText(TransactionsActivity.this, "Amount Added to your Budget", Toast.LENGTH_SHORT).show();
            Intent intent = getIntent();
            finish();
            startActivity(intent);
            }
        db.close();
    }

    @SuppressLint("UnsafeIntentLaunch")
    private void saveDataToOnline(String name, int budgetAmount) {
        loadingSpinner.show();
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String monthYear = currentDate.substring(0, 7);
        Call<List<Record>> targetCall = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
        targetCall.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();
                    boolean targetSet = false;
                    for (Record record : records) {
                        if (record instanceof Transaction) {
                            Transaction transaction = (Transaction) record;
                            String transTarget = String.valueOf(transaction.getTransTarget());
                            if (transTarget.trim().isEmpty()) {
                                Toast.makeText(TransactionsActivity.this, "Monthly Target not Set", Toast.LENGTH_SHORT).show();
                                loadingSpinner.hide();
                                return;
                            } else {
                                targetSet = true;
                            }

                        }
                    }
                    if (targetSet) {
                        Map<String, Object> budgetData = new HashMap<>();
                        budgetData.put("bud_name", name);
                        budgetData.put("bud_date", currentDate);
                        budgetData.put("bud_amount", budgetAmount);
                        budgetData.put("user_id", user_id);
                        Call<Void> insertCall = apiService.createRecord("budget",budgetData);
                        insertCall.enqueue(new retrofit2.Callback<Void>() {
                            @Override
                            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                                Log.e("Transaction", String.valueOf(response));
                                if (response.isSuccessful()) {
                                    updateTransactionOnline(monthYear, budgetAmount);
                                    Toast.makeText(TransactionsActivity.this, "Amount Added to your Budget", Toast.LENGTH_SHORT).show();
                                    Intent intent = getIntent();
                                    finish();
                                    startActivity(intent);
                                    loadingSpinner.hide();
                                } else {
                                    Toast.makeText(TransactionsActivity.this, "Error saving amount", Toast.LENGTH_SHORT).show();
                                    loadingSpinner.hide();
                                }
                            }
                            @Override
                            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                                Toast.makeText(TransactionsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                                loadingSpinner.hide();
                            }
                        });
                    }
                } else {
                    loadingSpinner.hide();
                    Toast.makeText(TransactionsActivity.this, "Failed to fetch transaction data", Toast.LENGTH_SHORT).show();
                }
            }
            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                loadingSpinner.hide();
                Toast.makeText(TransactionsActivity.this, "Network error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }


    private void updateTransactionAmounts(SQLiteDatabase db, String monthYear, int budgetAmount) {
        String query = "SELECT trans_balance,trans_budget FROM transactions WHERE strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{monthYear, String.valueOf(user_id)});
        float currentBalance = 0;
        float currentBudget = 0;
        if (cursor.moveToFirst()) {currentBalance = cursor.getFloat(0);
            currentBudget = cursor.getFloat(1);
        }
        cursor.close();
        float newBalance = currentBalance + budgetAmount;
        float newBudget = currentBudget + budgetAmount;
        ContentValues values = new ContentValues();
        values.put("trans_balance", newBalance);
        values.put("trans_budget", newBudget);
        int rowsUpdated = db.update("transactions", values, "strftime('%Y-%m', trans_month) LIKE ? AND user_id = ?", new String[]{monthYear,String.valueOf(user_id)});
        if (rowsUpdated == 0) {Log.w("DatabaseHelper", "No rows updated in transactions table.");}
    }
    private void updateBudgetInfo(int budgetAmount) {
        TextView budgetInfoTextView = findViewById(R.id.budget_info);
        String budgetInfo = getString(R.string.budget_info, budgetAmount);
        budgetInfoTextView.setText(budgetInfo);
    }
    private void updateTransactionOnline(String monthYear, int budgetAmount) {
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);

        Call<List<Record>> fetchCall = apiService.getRecords("transactions", monthYear, String.valueOf(user_id));
        fetchCall.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();
                    boolean updated = false;

                    for (Record record : records) {
                        if (record instanceof Transaction) {
                            Transaction transaction = (Transaction) record;
                            float currentBalance = (float) transaction.getBalance();
                            float currentBudget = (float) transaction.getTotal();

                            // Step 2: Update values
                            float newBalance = currentBalance + budgetAmount;
                            float newBudget = currentBudget + budgetAmount;

                            // Prepare data for update
                            Map<String, Object> updateData = new HashMap<>();
                            updateData.put("trans_balance", newBalance);
                            updateData.put("trans_budget", newBudget);

                            // Step 3: Send updated values back to the server
                            Call<Void> updateCall = apiService.updateRecord("transactions",String.valueOf(user_id), monthYear,updateData);
                            updateCall.enqueue(new retrofit2.Callback<Void>() {
                                @Override
                                public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
                                    if (response.isSuccessful()) {
                                        Log.d("UpdateTransaction", "Transaction updated successfully.");
                                    } else {
                                        Log.e("UpdateTransaction", "Error updating transaction: " + response);
                                    }
                                    loadingSpinner.hide();
                                }

                                @Override
                                public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                                    Log.e("UpdateTransaction", "Network error updating transaction: " + t.getMessage());
                                    loadingSpinner.hide();
                                }
                            });
                            updated = true;
                            break;
                        }
                    }
                    if (!updated) {
                        Log.e("UpdateTransaction", "No transaction found to update.");
                    }

                } else {
                    Log.e("UpdateTransaction", "Error fetching transaction: " + response.message());
                    loadingSpinner.hide();
                }
            }

            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                Log.e("UpdateTransaction", "Network error fetching transaction: " + t.getMessage());
                loadingSpinner.hide();
            }
        });
    }


//    private void updateTransactionOnline(SQLiteDatabase db,String monthYear, int budgetAmount) {
//        loadingSpinner.show();
//        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
//        Map<String, Object> data = new HashMap<>();
//        data.put("trans_balance", budgetAmount);
//        data.put("trans_budget", budgetAmount);
//        Call<Void> call = apiService.updateRecord("transactions", monthYear,user_id,data);
//        call.enqueue(new retrofit2.Callback<Void>() {
//            @Override
//            public void onResponse(@NonNull Call<Void> call, @NonNull retrofit2.Response<Void> response) {
//                if (response.isSuccessful()) {
//                    Log.d("UpdateTransaction", "Transaction updated successfully.");
//                    loadingSpinner.hide();
//                } else {
//                    Log.e("UpdateTransaction", "Error updating transaction: " + response.message());
//                    loadingSpinner.hide();
//                }
//            }
//
//            @Override
//            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
//                Log.e("UpdateTransaction", "Network error saving locally");
//            }
//        });
//    }

    private int getTotalBudgetForCurrentMonth(SQLiteDatabase db) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM", Locale.getDefault());
        String currentMonth = sdf.format(new Date());
        String query = "SELECT SUM(bud_amount) FROM budget WHERE strftime('%Y-%m',bud_date) LIKE ? AND  user_id = ?";
        Cursor cursor = db.rawQuery(query, new String[]{currentMonth + "%",String.valueOf(user_id)});
        int totalAmount = 0;
        if (cursor.moveToFirst()) {
            totalAmount = cursor.getInt(0);
        }
        cursor.close();
        return totalAmount;
    }
    private void getTotalBudgetForCurrentOnline(){
        loadingSpinner.show();
        ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM", Locale.getDefault());
        String currentMonth = sdf.format(new Date());
        Call<List<Record>> call = apiService.getRecords("budget", currentMonth, String.valueOf(user_id));
        call.enqueue(new retrofit2.Callback<List<Record>>() {
            @Override
            public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Record> records = response.body();
                    int totalAmount = 0;
                    for (Record record : records) {
                        if (record instanceof Budgets) {
                            Budgets budget = (Budgets) record;
                            totalAmount += budget.getAmount();
                        }
                    }
                    updateBudgetInfo(totalAmount);
                    totalBudgetForCurrentMonth = totalAmount;
                    loadingSpinner.hide();
                } else {
                    Toast.makeText(getApplicationContext(), "Failed to fetch budget data", Toast.LENGTH_SHORT).show();
                    loadingSpinner.hide();
                }
            }
            @Override
            public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                Toast.makeText(getApplicationContext(), "Error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                loadingSpinner.hide();
            }
        });
    }

    private void fetchAndSetData() {
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            totalBudgetForCurrentMonth = getTotalBudgetForCurrentMonth(db);
            updateBudgetInfo(totalBudgetForCurrentMonth);
            String[] projection = {"bud_name","bud_date","bud_amount"};
            String selection = "user_id = ?";
            String[] selectionArgs = {String.valueOf(user_id)};
            Cursor cursor = db.query(
                    "budget",
                    projection,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    null
            );
            List<Budgets> BudgetsList = new ArrayList<>();
            while (cursor.moveToNext()) {
                String name = cursor.getString(cursor.getColumnIndexOrThrow("bud_name"));
                String date = cursor.getString(cursor.getColumnIndexOrThrow("bud_date"));
                int amount = cursor.getInt(cursor.getColumnIndexOrThrow("bud_amount"));
                // Process data
                Budgets expense = new Budgets(name,date,amount,user_id);
                BudgetsList.add(expense);
            }
            cursor.close();
            Gson gson = new Gson();
            String jsonItems = gson.toJson(BudgetsList);
            Type listType = new TypeToken<List<Budgets>>() {}.getType();
            List<Budgets> items = gson.fromJson(jsonItems, listType);
             Item2Adapter     Item2Adapter = new Item2Adapter(items);
            recyclerView.setAdapter(Item2Adapter);
        }
    private String trimDate(String dateTime) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            return outputFormat.format(inputFormat.parse(dateTime));
        } catch (Exception e) {
            e.printStackTrace();
            return dateTime; // Return the original if parsing fails
        }
    }
        private void fetchAndSetOnline() {
            loadingSpinner.show();
            ApiService apiService = RetrofitClient.getClient().create(ApiService.class);
            Call<List<Record>> call = apiService.getRecords("budget", null, String.valueOf(user_id));
            call.enqueue(new retrofit2.Callback<List<Record>>() {
                @Override
                public void onResponse(@NonNull Call<List<Record>> call, @NonNull retrofit2.Response<List<Record>> response) {
                    if (response.isSuccessful() && response.body() != null) {
                        List<Budgets> items = getBudgets(response);
                        assert items != null;
                        for (Budgets budget : items) {
                            budget.setDate(trimDate(budget.getDate()));
                        }
                        Item2Adapter Item2Adapter = new Item2Adapter(items);
                        recyclerView.setAdapter(Item2Adapter);
                        loadingSpinner.hide();
                    } else {
                        loadingSpinner.hide();
                        Toast.makeText(getApplicationContext(), "Failed to fetch data", Toast.LENGTH_SHORT).show();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<List<Record>> call, @NonNull Throwable t) {
                    loadingSpinner.hide();
                    Toast.makeText(getApplicationContext(), "Error: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });
        }

            private static @Nullable List<Budgets> getBudgets(@NonNull Response<List<Record>> response) {
                List<Record> records = response.body();
                List<Budgets> BudgetsList = new ArrayList<>();
                for (Record record : records) {
                    if (record instanceof Budgets) {
                        Budgets budget = (Budgets) record;
                        BudgetsList.add(budget);
                    }
                }
                Gson gson = new Gson();
                String jsonItems = gson.toJson(BudgetsList);
                Type listType = new TypeToken<List<Budgets>>() {}.getType();
                List<Budgets> items = gson.fromJson(jsonItems, listType);
                return items;
            }


}

