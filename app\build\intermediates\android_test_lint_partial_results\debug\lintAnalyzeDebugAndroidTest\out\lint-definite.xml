<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.5.1" type="incidents">

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="107"
            endLine="6"
            endColumn="23"
            endOffset="114"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="131"
            endLine="7"
            endColumn="23"
            endOffset="138"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="152"
            endLine="8"
            endColumn="20"
            endOffset="159"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="172"
            endLine="9"
            endColumn="20"
            endOffset="180"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="10"
            column="20"
            startOffset="201"
            endLine="10"
            endColumn="27"
            endOffset="208"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="12"
            column="22"
            startOffset="257"
            endLine="12"
            endColumn="29"
            endOffset="264"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="13"
            column="16"
            startOffset="281"
            endLine="13"
            endColumn="23"
            endOffset="288"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="32.1.2"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="14"
            column="15"
            startOffset="304"
            endLine="14"
            endColumn="23"
            endOffset="312"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1">
        <fix-replace
            description="Change to 23.2.1"
            family="Update versions"
            oldString="23.0.0"
            replacement="23.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="16"
            column="16"
            startOffset="356"
            endLine="16"
            endColumn="24"
            endOffset="364"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.𝑥 available, if upgrading to 8.10.1 is difficult: 8.5.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.5.1"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.5.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.5.1"
                replacement="8.5.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="$HOME/Desktop/BudgetTracker/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="14"
            endOffset="25"/>
    </incident>

</incidents>
