{"logs": [{"outputFile": "com.example.budgettracker.app-mergeDebugResources-57:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6d752ff60ad5917ff94fc88eb3d6b2b1\\transformed\\material-1.10.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "969,1044,1155,1244,1345,1452,1559,1658,1765,1868,1956,2080,2182,2284,2400,2502,2616,2744,2860,2982,3118,3238,3372,3492,3604,3819,3936,4060,4190,4312,4450,4584,4700", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1039,1150,1239,1340,1447,1554,1653,1760,1863,1951,2075,2177,2279,2395,2497,2611,2739,2855,2977,3113,3233,3367,3487,3599,3725,3931,4055,4185,4307,4445,4579,4695,4815"}}, {"source": "C:\\Users\\<USER>\\Desktop\\BudgetTracker\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "102", "endLines": "8", "endColumns": "12", "endOffsets": "465"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "8", "endColumns": "12", "endOffsets": "332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0628b54f3ccce73188f36881ce28e044\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "9,10,11,12,13,14,15,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "337,407,491,575,671,773,875,3730", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "402,486,570,666,768,870,964,3814"}}]}]}