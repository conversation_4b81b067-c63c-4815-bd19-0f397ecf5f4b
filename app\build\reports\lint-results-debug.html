<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 18 errors and 236 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Mon Jun 09 23:16:29 EAT 2025 by AGP (8.5.1)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#KotlinNullnessAnnotation"><i class="material-icons warning-icon">warning</i>Kotlin nullability annotation (93)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#AppBundleLocaleChanges"><i class="material-icons warning-icon">warning</i>App Bundle handling of runtime locale changes (1)</a>
      <a class="mdl-navigation__link" href="#AndroidGradlePluginVersion"><i class="material-icons warning-icon">warning</i>Obsolete Android Gradle Plugin Version (3)</a>
      <a class="mdl-navigation__link" href="#BomWithoutPlatform"><i class="material-icons warning-icon">warning</i>Using a BOM without platform call (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (29)</a>
      <a class="mdl-navigation__link" href="#MissingTranslation"><i class="material-icons error-icon">error</i>Incomplete translation (18)</a>
      <a class="mdl-navigation__link" href="#UnsafeIntentLaunch"><i class="material-icons warning-icon">warning</i>Launched Unsafe Intent (2)</a>
      <a class="mdl-navigation__link" href="#ObsoleteLayoutParam"><i class="material-icons warning-icon">warning</i>Obsolete layout params (2)</a>
      <a class="mdl-navigation__link" href="#VectorPath"><i class="material-icons warning-icon">warning</i>Long vector paths (4)</a>
      <a class="mdl-navigation__link" href="#InefficientWeight"><i class="material-icons warning-icon">warning</i>Inefficient layout weight (1)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (9)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (42)</a>
      <a class="mdl-navigation__link" href="#IconLauncherShape"><i class="material-icons warning-icon">warning</i>The launcher icon shape should use a distinct silhouette (5)</a>
      <a class="mdl-navigation__link" href="#IconLocation"><i class="material-icons warning-icon">warning</i>Image defined in density-independent drawable folder (1)</a>
      <a class="mdl-navigation__link" href="#TextFields"><i class="material-icons warning-icon">warning</i>Missing <code>inputType</code> (8)</a>
      <a class="mdl-navigation__link" href="#Autofill"><i class="material-icons warning-icon">warning</i>Use Autofill (9)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (6)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (4)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (12)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (2)</a>
      <a class="mdl-navigation__link" href="#RelativeOverlap"><i class="material-icons warning-icon">warning</i>Overlapping items in RelativeLayout (1)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">93</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#KotlinNullnessAnnotation">KotlinNullnessAnnotation</a>: Kotlin nullability annotation</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AppBundleLocaleChanges">AppBundleLocaleChanges</a>: App Bundle handling of runtime locale changes</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AndroidGradlePluginVersion">AndroidGradlePluginVersion</a>: Obsolete Android Gradle Plugin Version</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#BomWithoutPlatform">BomWithoutPlatform</a>: Using a BOM without platform call</td></tr>
<tr>
<td class="countColumn">29</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness:Messages">Correctness:Messages</a>
</td></tr>
<tr>
<td class="countColumn">18</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingTranslation">MissingTranslation</a>: Incomplete translation</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Security">Security</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnsafeIntentLaunch">UnsafeIntentLaunch</a>: Launched Unsafe Intent</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteLayoutParam">ObsoleteLayoutParam</a>: Obsolete layout params</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#VectorPath">VectorPath</a>: Long vector paths</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InefficientWeight">InefficientWeight</a>: Inefficient layout weight</td></tr>
<tr>
<td class="countColumn">9</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr>
<td class="countColumn">42</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLauncherShape">IconLauncherShape</a>: The launcher icon shape should use a distinct silhouette</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLocation">IconLocation</a>: Image defined in density-independent drawable folder</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#TextFields">TextFields</a>: Missing <code>inputType</code></td></tr>
<tr>
<td class="countColumn">9</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Autofill">Autofill</a>: Use Autofill</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">12</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RelativeOverlap">RelativeOverlap</a>: Overlapping items in RelativeLayout</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (39)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (39)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="KotlinNullnessAnnotation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="KotlinNullnessAnnotationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Kotlin nullability annotation</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:120</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 117 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 118 </span>
<span class="lineno"> 119 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 120 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 121 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span> &amp;&amp; response.body()!!.isNotEmpty()) {
<span class="lineno"> 122 </span>              <span class="keyword">for</span> (record <span class="keyword">in</span> response.body()!!) {
<span class="lineno"> 123 </span>                  <span class="keyword">if</span> (record <span class="keyword">is</span> Transaction) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:120</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 117 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 118 </span>
<span class="lineno"> 119 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 120 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 121 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span> &amp;&amp; response.body()!!.isNotEmpty()) {
<span class="lineno"> 122 </span>              <span class="keyword">for</span> (record <span class="keyword">in</span> response.body()!!) {
<span class="lineno"> 123 </span>                  <span class="keyword">if</span> (record <span class="keyword">is</span> Transaction) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:136</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 133 </span>          }
<span class="lineno"> 134 </span>      }
<span class="lineno"> 135 </span>
<span class="caretline"><span class="lineno"> 136 </span>      override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;</span>
<span class="lineno"> 137 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@DashboardActivity</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 138 </span>      }
<span class="lineno"> 139 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:136</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 133 </span>          }
<span class="lineno"> 134 </span>      }
<span class="lineno"> 135 </span>
<span class="caretline"><span class="lineno"> 136 </span>      override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;</span>
<span class="lineno"> 137 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@DashboardActivity</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 138 </span>      }
<span class="lineno"> 139 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:311</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 308 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"expenses"</span>, monthyear, user_id.toString())
<span class="lineno"> 309 </span>  
<span class="lineno"> 310 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 311 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 312 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 313 </span>              <span class="keyword">val</span> items = getExpenses(response)
<span class="lineno"> 314 </span>              <span class="keyword">if</span> (items != <span class="keyword">null</span>) {
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="KotlinNullnessAnnotationDivLink" onclick="reveal('KotlinNullnessAnnotationDiv');" />+ 88 More Occurrences...</button>
<div id="KotlinNullnessAnnotationDiv" style="display: none">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:311</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 308 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"expenses"</span>, monthyear, user_id.toString())
<span class="lineno"> 309 </span>  
<span class="lineno"> 310 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 311 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 312 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 313 </span>              <span class="keyword">val</span> items = getExpenses(response)
<span class="lineno"> 314 </span>              <span class="keyword">if</span> (items != <span class="keyword">null</span>) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:330</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 327 </span>      }
<span class="lineno"> 328 </span>  }
<span class="lineno"> 329 </span>
<span class="caretline"><span class="lineno"> 330 </span>  override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 331 </span>      Toast.makeText(applicationContext, <span class="string">"Error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 332 </span>      loadingSpinner.hide()
<span class="lineno"> 333 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:330</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 327 </span>      }
<span class="lineno"> 328 </span>  }
<span class="lineno"> 329 </span>
<span class="caretline"><span class="lineno"> 330 </span>  override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 331 </span>      Toast.makeText(applicationContext, <span class="string">"Error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 332 </span>      loadingSpinner.hide()
<span class="lineno"> 333 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt">../../src/main/kotlin/com/example/budgettracker/DashboardActivity.kt</a>:338</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 335 </span>    }
<span class="lineno"> 336 </span>
<span class="lineno"> 337 </span>    <span class="annotation">@Nullable</span>
<span class="caretline"><span class="lineno"> 338 </span>    private <span class="keyword">fun</span> getExpenses(<span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>): List&lt;Expenses>? {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 339 </span>        <span class="keyword">val</span> records = response.body()
<span class="lineno"> 340 </span>        <span class="keyword">val</span> expensesList = mutableListOf&lt;Expenses>()
<span class="lineno"> 341 </span>        <span class="keyword">val</span> amounts = mutableListOf&lt;Int>()
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:128</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  
<span class="lineno"> 126 </span>  <span class="keyword">val</span> call = apiService.createRecord(<span class="string">"expenses"</span>, data)
<span class="lineno"> 127 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 128 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> response: Response&lt;Void>) {</span>
<span class="lineno"> 129 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 130 </span>              <span class="keyword">val</span> monthYear = date.substring(<span class="number">0</span>, <span class="number">7</span>)
<span class="lineno"> 131 </span>              updateTransactionOnline(monthYear, budgetAmount)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:128</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 125 </span>  
<span class="lineno"> 126 </span>  <span class="keyword">val</span> call = apiService.createRecord(<span class="string">"expenses"</span>, data)
<span class="lineno"> 127 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 128 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;Void>) {</span>
<span class="lineno"> 129 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 130 </span>              <span class="keyword">val</span> monthYear = date.substring(<span class="number">0</span>, <span class="number">7</span>)
<span class="lineno"> 131 </span>              updateTransactionOnline(monthYear, budgetAmount)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:142</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>      }
<span class="lineno"> 140 </span>  }
<span class="lineno"> 141 </span>
<span class="caretline"><span class="lineno"> 142 </span>  override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@ExpensesActivity</span>, <span class="string">"Network error. Saving locally."</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 144 </span>      <span class="keyword">val</span> db = dbHelper.writableDatabase
<span class="lineno"> 145 </span>      saveDataToDatabase(db, selectedCategory, date, note, budgetAmount, budgetAmount)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:142</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>      }
<span class="lineno"> 140 </span>  }
<span class="lineno"> 141 </span>
<span class="caretline"><span class="lineno"> 142 </span>  override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@ExpensesActivity</span>, <span class="string">"Network error. Saving locally."</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 144 </span>      <span class="keyword">val</span> db = dbHelper.writableDatabase
<span class="lineno"> 145 </span>      saveDataToDatabase(db, selectedCategory, date, note, budgetAmount, budgetAmount)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:204</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 201 </span>
<span class="lineno"> 202 </span>  <span class="keyword">val</span> fetchCall = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 203 </span>  fetchCall.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 204 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 205 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 206 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 207 </span>              <span class="keyword">var</span> updated = <span class="keyword">false</span></pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:204</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 201 </span>
<span class="lineno"> 202 </span>  <span class="keyword">val</span> fetchCall = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 203 </span>  fetchCall.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 204 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 205 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 206 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 207 </span>              <span class="keyword">var</span> updated = <span class="keyword">false</span></pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:219</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 216 </span>
<span class="lineno"> 217 </span>  <span class="keyword">val</span> updateCall = apiService.updateRecord(<span class="string">"transactions"</span>, user_id!!, monthYear, data)
<span class="lineno"> 218 </span>  updateCall.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 219 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> response: Response&lt;Void>) {</span>
<span class="lineno"> 220 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 221 </span>              Log.d(<span class="string">"ExpensesActivity"</span>, <span class="string">"Transaction balance updated successfully"</span>)
<span class="lineno"> 222 </span>          } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:219</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 216 </span>
<span class="lineno"> 217 </span>  <span class="keyword">val</span> updateCall = apiService.updateRecord(<span class="string">"transactions"</span>, user_id!!, monthYear, data)
<span class="lineno"> 218 </span>  updateCall.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 219 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;Void>) {</span>
<span class="lineno"> 220 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 221 </span>              Log.d(<span class="string">"ExpensesActivity"</span>, <span class="string">"Transaction balance updated successfully"</span>)
<span class="lineno"> 222 </span>          } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:227</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 224 </span>          }
<span class="lineno"> 225 </span>      }
<span class="lineno"> 226 </span>
<span class="caretline"><span class="lineno"> 227 </span>      override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> t: Throwable) {</span>
<span class="lineno"> 228 </span>          Log.e(<span class="string">"ExpensesActivity"</span>, <span class="string">"Network error updating transaction: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 229 </span>      }
<span class="lineno"> 230 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:227</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 224 </span>          }
<span class="lineno"> 225 </span>      }
<span class="lineno"> 226 </span>
<span class="caretline"><span class="lineno"> 227 </span>      override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {</span>
<span class="lineno"> 228 </span>          Log.e(<span class="string">"ExpensesActivity"</span>, <span class="string">"Network error updating transaction: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 229 </span>      }
<span class="lineno"> 230 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:244</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>            }
<span class="lineno"> 242 </span>        }
<span class="lineno"> 243 </span>
<span class="caretline"><span class="lineno"> 244 </span>        override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>            Log.e(<span class="string">"ExpensesActivity"</span>, <span class="string">"Network error fetching transactions: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 246 </span>        }
<span class="lineno"> 247 </span>    })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:244</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>            }
<span class="lineno"> 242 </span>        }
<span class="lineno"> 243 </span>
<span class="caretline"><span class="lineno"> 244 </span>        override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>            Log.e(<span class="string">"ExpensesActivity"</span>, <span class="string">"Network error fetching transactions: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 246 </span>        }
<span class="lineno"> 247 </span>    })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt">../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt</a>:12</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewHolder</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span><span class="keyword">class</span> Item2Adapter(private <span class="keyword">val</span> budgetsList: List&lt;Budgets>) : RecyclerView.Adapter&lt;Item2Adapter.ViewHolder>() {
<span class="lineno"> 11 </span>
<span class="caretline"><span class="lineno"> 12 </span>    <span class="warning"><span class="annotation">@NonNull</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>    override <span class="keyword">fun</span> onCreateViewHolder(<span class="annotation">@NonNull</span> parent: ViewGroup, viewType: Int): ViewHolder {
<span class="lineno"> 14 </span>        <span class="keyword">val</span> view = LayoutInflater.from(parent.context)
<span class="lineno"> 15 </span>            .inflate(R.layout.list_bug, parent, <span class="keyword">false</span>)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt">../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt</a>:13</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewGroup</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 10 </span><span class="keyword">class</span> Item2Adapter(private <span class="keyword">val</span> budgetsList: List&lt;Budgets>) : RecyclerView.Adapter&lt;Item2Adapter.ViewHolder>() {
<span class="lineno"> 11 </span>
<span class="lineno"> 12 </span>    <span class="annotation">@NonNull</span>
<span class="caretline"><span class="lineno"> 13 </span>    override <span class="keyword">fun</span> onCreateViewHolder(<span class="warning"><span class="annotation">@NonNull</span></span> parent: ViewGroup, viewType: Int): ViewHolder {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="keyword">val</span> view = LayoutInflater.from(parent.context)
<span class="lineno"> 15 </span>            .inflate(R.layout.list_bug, parent, <span class="keyword">false</span>)
<span class="lineno"> 16 </span>        <span class="keyword">return</span> ViewHolder(view)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt">../../src/main/kotlin/com/example/budgettracker/Item2Adapter.kt</a>:19</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewHolder</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>        <span class="keyword">return</span> ViewHolder(view)
<span class="lineno"> 17 </span>    }
<span class="lineno"> 18 </span>
<span class="caretline"><span class="lineno"> 19 </span>    override <span class="keyword">fun</span> onBindViewHolder(<span class="warning"><span class="annotation">@NonNull</span></span> holder: ViewHolder, position: Int) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>        <span class="keyword">val</span> budget = budgetsList[position]
<span class="lineno"> 21 </span>        holder.nameTextView.text = budget.getName()
<span class="lineno"> 22 </span>        holder.dateTextView.text = budget.getDate()
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt">../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt</a>:12</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewHolder</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span><span class="keyword">class</span> ItemAdapter(private <span class="keyword">val</span> expensesList: List&lt;Expenses>) : RecyclerView.Adapter&lt;ItemAdapter.ViewHolder>() {
<span class="lineno"> 11 </span>
<span class="caretline"><span class="lineno"> 12 </span>    <span class="warning"><span class="annotation">@NonNull</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>    override <span class="keyword">fun</span> onCreateViewHolder(<span class="annotation">@NonNull</span> parent: ViewGroup, viewType: Int): ViewHolder {
<span class="lineno"> 14 </span>        <span class="keyword">val</span> view = LayoutInflater.from(parent.context)
<span class="lineno"> 15 </span>            .inflate(R.layout.list_item, parent, <span class="keyword">false</span>)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt">../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt</a>:13</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewGroup</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 10 </span><span class="keyword">class</span> ItemAdapter(private <span class="keyword">val</span> expensesList: List&lt;Expenses>) : RecyclerView.Adapter&lt;ItemAdapter.ViewHolder>() {
<span class="lineno"> 11 </span>
<span class="lineno"> 12 </span>    <span class="annotation">@NonNull</span>
<span class="caretline"><span class="lineno"> 13 </span>    override <span class="keyword">fun</span> onCreateViewHolder(<span class="warning"><span class="annotation">@NonNull</span></span> parent: ViewGroup, viewType: Int): ViewHolder {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="keyword">val</span> view = LayoutInflater.from(parent.context)
<span class="lineno"> 15 </span>            .inflate(R.layout.list_item, parent, <span class="keyword">false</span>)
<span class="lineno"> 16 </span>        <span class="keyword">return</span> ViewHolder(view)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt">../../src/main/kotlin/com/example/budgettracker/ItemAdapter.kt</a>:19</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>ViewHolder</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>        <span class="keyword">return</span> ViewHolder(view)
<span class="lineno"> 17 </span>    }
<span class="lineno"> 18 </span>
<span class="caretline"><span class="lineno"> 19 </span>    override <span class="keyword">fun</span> onBindViewHolder(<span class="warning"><span class="annotation">@NonNull</span></span> holder: ViewHolder, position: Int) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>        <span class="keyword">val</span> expense = expensesList[position]
<span class="lineno"> 21 </span>        holder.nameTextView.text = expense.name
<span class="lineno"> 22 </span>        holder.dateTextView.text = expense.date <span class="comment">// Set the date</span></pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:168</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 165 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"user"</span>, currentDate, userId)
<span class="lineno"> 166 </span>
<span class="lineno"> 167 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 168 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 169 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 170 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 171 </span>              <span class="keyword">if</span> (records.isNotEmpty()) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:168</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 165 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"user"</span>, currentDate, userId)
<span class="lineno"> 166 </span>
<span class="lineno"> 167 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 168 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 169 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 170 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 171 </span>              <span class="keyword">if</span> (records.isNotEmpty()) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:186</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>        }
<span class="lineno"> 184 </span>    }
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>    override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>        Log.w(<span class="string">"MainActivity"</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 188 </span>        callback.onDisplayNameRetrieved(<span class="keyword">null</span>)
<span class="lineno"> 189 </span>    }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:186</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>        }
<span class="lineno"> 184 </span>    }
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>    override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>        Log.w(<span class="string">"MainActivity"</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 188 </span>        callback.onDisplayNameRetrieved(<span class="keyword">null</span>)
<span class="lineno"> 189 </span>    }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:217</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 214 </span>  <span class="keyword">val</span> call = apiService.updateRecord(<span class="string">"user"</span>, userId, dateFormat.toString(), updatedValues)
<span class="lineno"> 215 </span>  
<span class="lineno"> 216 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 217 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> response: Response&lt;Void>) {&nbsp;</span>
<span class="lineno"> 218 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 219 </span>              Log.d(<span class="string">"SaveUser"</span>, <span class="string">"User status updated successfully."</span>)
<span class="lineno"> 220 </span>          } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:217</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 214 </span>  <span class="keyword">val</span> call = apiService.updateRecord(<span class="string">"user"</span>, userId, dateFormat.toString(), updatedValues)
<span class="lineno"> 215 </span>  
<span class="lineno"> 216 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 217 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;Void>) {&nbsp;</span>
<span class="lineno"> 218 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 219 </span>              Log.d(<span class="string">"SaveUser"</span>, <span class="string">"User status updated successfully."</span>)
<span class="lineno"> 220 </span>          } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:225</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 222 </span>                }
<span class="lineno"> 223 </span>            }
<span class="lineno"> 224 </span>
<span class="caretline"><span class="lineno"> 225 </span>            override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 226 </span>                Log.e(<span class="string">"SaveUser"</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 227 </span>            }
<span class="lineno"> 228 </span>        })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:225</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 222 </span>                }
<span class="lineno"> 223 </span>            }
<span class="lineno"> 224 </span>
<span class="caretline"><span class="lineno"> 225 </span>            override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 226 </span>                Log.e(<span class="string">"SaveUser"</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>)
<span class="lineno"> 227 </span>            }
<span class="lineno"> 228 </span>        })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:234</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>CharSequence</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 231 </span>  private <span class="keyword">fun</span> startFingerprintAuthentication() {
<span class="lineno"> 232 </span>      <span class="keyword">val</span> executor: Executor = ContextCompat.getMainExecutor(<span class="keyword">this</span>)
<span class="lineno"> 233 </span>      <span class="keyword">val</span> biometricPrompt = BiometricPrompt(<span class="keyword">this</span><span class="annotation">@MainActivity</span>, executor, <span class="keyword">object</span> : BiometricPrompt.AuthenticationCallback() {
<span class="caretline"><span class="lineno"> 234 </span>          override <span class="keyword">fun</span> onAuthenticationError(errorCode: Int, <span class="warning"><span class="annotation">@NonNull</span></span> errString: CharSequence) {&nbsp;&nbsp;</span>
<span class="lineno"> 235 </span>              <span class="keyword">super</span>.onAuthenticationError(errorCode, errString)
<span class="lineno"> 236 </span>              Toast.makeText(<span class="keyword">this</span><span class="annotation">@MainActivity</span>, <span class="string">"Authentication error: $errString"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 237 </span>          }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/MainActivity.kt">../../src/main/kotlin/com/example/budgettracker/MainActivity.kt</a>:239</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>BiometricPrompt.AuthenticationResult</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 236 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@MainActivity</span>, <span class="string">"Authentication error: $errString"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 237 </span>  }
<span class="lineno"> 238 </span>
<span class="caretline"><span class="lineno"> 239 </span>  override <span class="keyword">fun</span> onAuthenticationSucceeded(<span class="warning"><span class="annotation">@NonNull</span></span> result: BiometricPrompt.AuthenticationResult) {</span>
<span class="lineno"> 240 </span>      <span class="keyword">super</span>.onAuthenticationSucceeded(result)
<span class="lineno"> 241 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@MainActivity</span>, <span class="string">"Authentication succeeded!"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 242 </span>      <span class="keyword">val</span> intent = Intent(<span class="keyword">this</span><span class="annotation">@MainActivity</span>, DashboardActivity::<span class="keyword">class</span>.java)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt">../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt</a>:84</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno">  81 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno">  82 </span>  
<span class="lineno">  83 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno">  84 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno">  85 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno">  86 </span>              <span class="keyword">val</span> records = response.body()!!
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt">../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt</a>:84</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno">  81 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno">  82 </span>  
<span class="lineno">  83 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno">  84 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno">  85 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno">  86 </span>              <span class="keyword">val</span> records = response.body()!!
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt">../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt</a>:116</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 113 </span>          }
<span class="lineno"> 114 </span>      }
<span class="lineno"> 115 </span>
<span class="caretline"><span class="lineno"> 116 </span>      override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 117 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@NotificationActivity</span>, <span class="string">"Error with notification"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 118 </span>      }
<span class="lineno"> 119 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt">../../src/main/kotlin/com/example/budgettracker/NotificationActivity.kt</a>:116</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 113 </span>          }
<span class="lineno"> 114 </span>      }
<span class="lineno"> 115 </span>
<span class="caretline"><span class="lineno"> 116 </span>      override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 117 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@NotificationActivity</span>, <span class="string">"Error with notification"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 118 </span>      }
<span class="lineno"> 119 </span>  })
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt">../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt</a>:177</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 174 </span>  
<span class="lineno"> 175 </span>  <span class="keyword">val</span> call = apiService.createRecord(<span class="string">"user"</span>, data)
<span class="lineno"> 176 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 177 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> response: Response&lt;Void>) {&nbsp;</span>
<span class="lineno"> 178 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 179 </span>              Toast.makeText(<span class="keyword">this</span><span class="annotation">@RegisterActivity</span>, <span class="string">"Data saved successfully"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 180 </span>              loadingSpinner.hide()
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt">../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt</a>:177</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 174 </span>  
<span class="lineno"> 175 </span>  <span class="keyword">val</span> call = apiService.createRecord(<span class="string">"user"</span>, data)
<span class="lineno"> 176 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;Void> {
<span class="caretline"><span class="lineno"> 177 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;Void>) {&nbsp;</span>
<span class="lineno"> 178 </span>          <span class="keyword">if</span> (response.isSuccessful) {
<span class="lineno"> 179 </span>              Toast.makeText(<span class="keyword">this</span><span class="annotation">@RegisterActivity</span>, <span class="string">"Data saved successfully"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 180 </span>              loadingSpinner.hide()
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt">../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt</a>:186</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;Void></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>      }
<span class="lineno"> 184 </span>  }
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>  override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;Void>, <span class="annotation">@NonNull</span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@RegisterActivity</span>, <span class="string">"Network error. Saving locally."</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 188 </span>      saveUserData(email, username, uid)
<span class="lineno"> 189 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt">../../src/main/kotlin/com/example/budgettracker/RegisterActivity.kt</a>:186</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>      }
<span class="lineno"> 184 </span>  }
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>  override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;Void>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@RegisterActivity</span>, <span class="string">"Network error. Saving locally."</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 188 </span>      saveUserData(email, username, uid)
<span class="lineno"> 189 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:167</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 164 </span>  <span class="keyword">val</span> monthYear = outputFormat.format(parsedDate)
<span class="lineno"> 165 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 166 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 167 </span>      override <span class="keyword">fun</span> onResponse(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 168 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 169 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 170 </span>              <span class="keyword">var</span> balance = <span class="number">0.0</span></pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:167</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Response&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 164 </span>  <span class="keyword">val</span> monthYear = outputFormat.format(parsedDate)
<span class="lineno"> 165 </span>  <span class="keyword">val</span> call = apiService.getRecords(<span class="string">"transactions"</span>, monthYear, user_id.toString())
<span class="lineno"> 166 </span>  call.enqueue(<span class="keyword">object</span> : Callback&lt;List&lt;Record>> {
<span class="caretline"><span class="lineno"> 167 </span>      override <span class="keyword">fun</span> onResponse(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> response: Response&lt;List&lt;Record>>) {</span>
<span class="lineno"> 168 </span>          <span class="keyword">if</span> (response.isSuccessful &amp;&amp; response.body() != <span class="keyword">null</span>) {
<span class="lineno"> 169 </span>              <span class="keyword">val</span> records = response.body()!!
<span class="lineno"> 170 </span>              <span class="keyword">var</span> balance = <span class="number">0.0</span></pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:195</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Call&lt;List&lt;Record>></code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 192 </span>      }
<span class="lineno"> 193 </span>  }
<span class="lineno"> 194 </span>
<span class="caretline"><span class="lineno"> 195 </span>  override <span class="keyword">fun</span> onFailure(<span class="warning"><span class="annotation">@NonNull</span></span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {</span>
<span class="lineno"> 196 </span>      loadingSpinner.hide()
<span class="lineno"> 197 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@SettingsActivity</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 198 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:195</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>Throwable</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 192 </span>      }
<span class="lineno"> 193 </span>  }
<span class="lineno"> 194 </span>
<span class="caretline"><span class="lineno"> 195 </span>  override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="warning"><span class="annotation">@NonNull</span></span> t: Throwable) {</span>
<span class="lineno"> 196 </span>      loadingSpinner.hide()
<span class="lineno"> 197 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@SettingsActivity</span>, <span class="string">"Network error: ${</span>t.message<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 198 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:209</span>: <span class="message">Do not use <code>@NonNull</code> in Kotlin; the nullability is already implied by the Kotlin type <code>KeyEvent</code> <b>not</b> ending with <code>?</code></span><br /><pre class="errorlines">
<span class="lineno"> 206 </span>        }
<span class="lineno"> 207 </span>    }
<span class="lineno"> 208 </span>
<span class="caretline"><span class="lineno"> 209 </span>    override <span class="keyword">fun</span> onKeyDown(keyCode: Int, <span class="warning"><span class="annotation">@NonNull</span></span> event: KeyEvent): Boolean {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 210 </span>        <span class="keyword">if</span> (keyCode == KeyEvent.KEYCODE_BACK) {
<span class="lineno"> 211 </span>            startActivity(Intent(<span class="keyword">this</span><span class="annotation">@SettingsActivity</span>, MainActivity::<span class="keyword">class</span>.java))
<span class="lineno"> 212 </span>            finish()
</pre>

<br/><b>NOTE: 43 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationKotlinNullnessAnnotation" style="display: none;">
In Kotlin, nullness is part of the type system; <code>s: String</code> is <b>never</b> null and <code>s: String?</code> is sometimes null, whether or not you add in additional annotations stating <code>@NonNull</code> or <code>@Nullable</code>. These are likely copy/paste mistakes, and are misleading.<br/>To suppress this error, use the issue id "KotlinNullnessAnnotation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">KotlinNullnessAnnotation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationKotlinNullnessAnnotationLink" onclick="reveal('explanationKotlinNullnessAnnotation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="KotlinNullnessAnnotationCardLink" onclick="hideid('KotlinNullnessAnnotationCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:14</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the <code>android.os.Build.VERSION_CODES</code> javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    defaultConfig {
<span class="lineno"> 12 </span>        applicationId = "com.example.budgettracker"
<span class="lineno"> 13 </span>        minSdk = 24
<span class="caretline"><span class="lineno"> 14 </span>        <span class="warning">targetSdk = 34</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>        versionCode = 1
<span class="lineno"> 16 </span>        versionName = "1.0"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AppBundleLocaleChanges"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AppBundleLocaleChangesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">App Bundle handling of runtime locale changes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/LanguageActivity.kt">../../src/main/kotlin/com/example/budgettracker/LanguageActivity.kt</a>:41</span>: <span class="message">Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the <code>bundle</code> configuration</span><br /><pre class="errorlines">
<span class="lineno"> 38 </span>        <span class="keyword">val</span> locale = Locale(languageCode)
<span class="lineno"> 39 </span>        Locale.setDefault(locale)
<span class="lineno"> 40 </span>        <span class="keyword">val</span> config = Configuration()
<span class="caretline"><span class="lineno"> 41 </span>        <span class="warning">config.setLocale(locale)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 42 </span>        resources.updateConfiguration(config, resources.displayMetrics)
<span class="lineno"> 43 </span>        
<span class="lineno"> 44 </span>        <span class="keyword">val</span> prefs = getSharedPreferences(<span class="string">"app_prefs"</span>, MODE_PRIVATE)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAppBundleLocaleChanges" style="display: none;">
When changing locales at runtime (e.g. to provide an in-app language switcher), the Android App Bundle must be configured to not split by locale or the Play Core library must be used to download additional locales at runtime.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes">https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes</a>
</div>To suppress this error, use the issue id "AppBundleLocaleChanges" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AppBundleLocaleChanges</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAppBundleLocaleChangesLink" onclick="reveal('explanationAppBundleLocaleChanges');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AppBundleLocaleChangesCardLink" onclick="hideid('AppBundleLocaleChangesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AndroidGradlePluginVersion"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AndroidGradlePluginVersionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Android Gradle Plugin Version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.&#55349;&#56421; available, if upgrading to 8.10.1 is difficult: 8.5.2)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.&#55349;&#56421; available, if upgrading to 8.10.1 is difficult: 8.5.2)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.5.1 is available: 8.10.1. (There is also a newer version of 8.5.&#55349;&#56421; available, if upgrading to 8.10.1 is difficult: 8.5.2)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAndroidGradlePluginVersion" style="display: none;">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AndroidGradlePluginVersion</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAndroidGradlePluginVersionLink" onclick="reveal('explanationAndroidGradlePluginVersion');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AndroidGradlePluginVersionCardLink" onclick="hideid('AndroidGradlePluginVersionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="BomWithoutPlatform"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="BomWithoutPlatformCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using a BOM without platform call</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:60</span>: <span class="message">BOM should be added with a call to platform()</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="caretline"><span class="lineno"> 60 </span>    implementation(<span class="warning">"com.google.firebase:firebase-bom:33.1.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 61 </span>    implementation ("androidx.work:work-runtime:2.8.0")
<span class="lineno"> 62 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationBomWithoutPlatform" style="display: none;">
When including a BOM, the dependency's coordinates must be wrapped in a call to <code>platform()</code> for Gradle to interpret it correctly.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/r/tools/gradle-bom-docs">https://developer.android.com/r/tools/gradle-bom-docs</a>
</div>To suppress this error, use the issue id "BomWithoutPlatform" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">BomWithoutPlatform</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationBomWithoutPlatformLink" onclick="reveal('explanationBomWithoutPlatform');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="BomWithoutPlatformCardLink" onclick="hideid('BomWithoutPlatformCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:60</span>: <span class="message">A newer version of com.google.firebase:firebase-bom than 33.1.2 is available: 33.15.0</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="caretline"><span class="lineno"> 60 </span>    implementation(<span class="warning">"com.google.firebase:firebase-bom:33.1.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 61 </span>    implementation ("androidx.work:work-runtime:2.8.0")
<span class="lineno"> 62 </span>
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:61</span>: <span class="message">A newer version of androidx.work:work-runtime than 2.8.0 is available: 2.10.1</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 60 </span>    implementation("com.google.firebase:firebase-bom:33.1.2")
<span class="caretline"><span class="lineno"> 61 </span>    implementation (<span class="warning">"androidx.work:work-runtime:2.8.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>
<span class="lineno"> 63 </span>
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "1.9.0"
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="GradleDependencyDivLink" onclick="reveal('GradleDependencyDiv');" />+ 24 More Occurrences...</button>
<div id="GradleDependencyDiv" style="display: none">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>biometric = "1.2.0-alpha05"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>appcompat = <span class="warning">"1.6.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>appcompat = <span class="warning">"1.6.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>appcompat = <span class="warning">"1.6.1"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="caretline"><span class="lineno">  9 </span>material = <span class="warning">"1.10.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="caretline"><span class="lineno">  9 </span>material = <span class="warning">"1.10.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="caretline"><span class="lineno">  9 </span>material = <span class="warning">"1.10.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="caretline"><span class="lineno"> 10 </span>constraintlayout = <span class="warning">"2.1.4"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="caretline"><span class="lineno"> 10 </span>constraintlayout = <span class="warning">"2.1.4"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>appcompat = "1.6.1"
<span class="lineno">  9 </span>material = "1.10.0"
<span class="caretline"><span class="lineno"> 10 </span>constraintlayout = <span class="warning">"2.1.4"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:12</span>: <span class="message">A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="caretline"><span class="lineno"> 12 </span>navigationFragment = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:12</span>: <span class="message">A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="caretline"><span class="lineno"> 12 </span>navigationFragment = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:12</span>: <span class="message">A newer version of androidx.navigation:navigation-fragment than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>material = "1.10.0"
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="caretline"><span class="lineno"> 12 </span>navigationFragment = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:13</span>: <span class="message">A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="caretline"><span class="lineno"> 13 </span>navigationUi = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:13</span>: <span class="message">A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="caretline"><span class="lineno"> 13 </span>navigationUi = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:13</span>: <span class="message">A newer version of androidx.navigation:navigation-ui than 2.6.0 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>constraintlayout = "2.1.4"
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="caretline"><span class="lineno"> 13 </span>navigationUi = <span class="warning">"2.6.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:14</span>: <span class="message">A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="caretline"><span class="lineno"> 14 </span>firebaseBom = <span class="warning">"32.1.2"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
<span class="lineno"> 17 </span>contentpager = "1.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:14</span>: <span class="message">A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="caretline"><span class="lineno"> 14 </span>firebaseBom = <span class="warning">"32.1.2"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
<span class="lineno"> 17 </span>contentpager = "1.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:14</span>: <span class="message">A newer version of com.google.firebase:firebase-bom than 32.1.2 is available: 33.15.0</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>mpandroidchart = "3.1.0"
<span class="lineno"> 12 </span>navigationFragment = "2.6.0"
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="caretline"><span class="lineno"> 14 </span>firebaseBom = <span class="warning">"32.1.2"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="lineno"> 16 </span>firebaseAuth = "23.0.0"
<span class="lineno"> 17 </span>contentpager = "1.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:16</span>: <span class="message">A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="caretline"><span class="lineno"> 16 </span>firebaseAuth = <span class="warning">"23.0.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>contentpager = "1.0.0"
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>[libraries]
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:16</span>: <span class="message">A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="caretline"><span class="lineno"> 16 </span>firebaseAuth = <span class="warning">"23.0.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>contentpager = "1.0.0"
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>[libraries]
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:16</span>: <span class="message">A newer version of com.google.firebase:firebase-auth than 23.0.0 is available: 23.2.1</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>navigationUi = "2.6.0"
<span class="lineno"> 14 </span>firebaseBom = "32.1.2"
<span class="lineno"> 15 </span>firebaseCommon = "21.0.0"
<span class="caretline"><span class="lineno"> 16 </span>firebaseAuth = <span class="warning">"23.0.0"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>contentpager = "1.0.0"
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>[libraries]
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness:Messages"></a>
<a name="MissingTranslation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingTranslationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Incomplete translation</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:55</span>: <span class="message">"budget_info" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 52 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"heading_list_dash"</span>>Expenditures<span class="tag">&lt;/string></span>
<span class="lineno"> 53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"type"</span>>Type<span class="tag">&lt;/string></span>
<span class="lineno"> 54 </span><span class="comment">&lt;!--    &lt;string name="budget_info">Your budget for this month is $%.2f&lt;/string>--></span>
<span class="caretline"><span class="lineno"> 55 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"budget_info"</span></span>>Your budget for this month is $%d<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 56 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add"</span>>Set<span class="tag">&lt;/string></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add_money"</span>>Add money<span class="tag">&lt;/string></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"settings"</span>>settings<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:66</span>: <span class="message">"zulu" is not translated in "af" (Afrikaans) or "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 63 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"english"</span>>English<span class="tag">&lt;/string></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"language"</span>>Language<span class="tag">&lt;/string></span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"notifications"</span>>Notifications<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 66 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"zulu"</span></span>>Zulu<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 67 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"name_bug"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"bug_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:67</span>: <span class="message">"name_bug" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 64 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"language"</span>>Language<span class="tag">&lt;/string></span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"notifications"</span>>Notifications<span class="tag">&lt;/string></span>
<span class="lineno"> 66 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"zulu"</span>>Zulu<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 67 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"name_bug"</span></span>>Name<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"bug_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:68</span>: <span class="message">"bug_amount" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 65 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"notifications"</span>>Notifications<span class="tag">&lt;/string></span>
<span class="lineno"> 66 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"zulu"</span>>Zulu<span class="tag">&lt;/string></span>
<span class="lineno"> 67 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"name_bug"</span>>Name<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"bug_amount"</span></span>>Amount<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:69</span>: <span class="message">"exp_amount" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 66 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"zulu"</span>>Zulu<span class="tag">&lt;/string></span>
<span class="lineno"> 67 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"name_bug"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"bug_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"exp_amount"</span></span>>Amount<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="MissingTranslationDivLink" onclick="reveal('MissingTranslationDiv');" />+ 13 More Occurrences...</button>
<div id="MissingTranslationDiv" style="display: none">
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:70</span>: <span class="message">"ext_name" is not translated in "af" (Afrikaans) or "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 67 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"name_bug"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"bug_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"ext_name"</span></span>>Name<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:71</span>: <span class="message">"exp_date" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 68 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"bug_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"exp_date"</span></span>>exp_date<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:72</span>: <span class="message">"date_x" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 69 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_amount"</span>>Amount<span class="tag">&lt;/string></span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"date_x"</span></span>>Date<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:73</span>: <span class="message">"main_cash" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"main_cash"</span></span>>Main_cash<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:74</span>: <span class="message">"_0" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"_0"</span></span>>$0<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:75</span>: <span class="message">"month" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"month"</span></span>>This month<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:76</span>: <span class="message">"pick_date" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"main_cash"</span>>Main_cash<span class="tag">&lt;/string></span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"pick_date"</span></span>>Pick Date<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:77</span>: <span class="message">"budget" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"budget"</span></span>>Budget<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:78</span>: <span class="message">"target" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"target"</span></span>>Target<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
<span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set"</span>>Set<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:79</span>: <span class="message">"balance" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"balance"</span></span>>Balance<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
<span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set"</span>>Set<span class="tag">&lt;/string></span>
<span class="lineno"> 82 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sign_up"</span>>Sign Up<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:80</span>: <span class="message">"set_new_target" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 77 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget"</span>>Budget<span class="tag">&lt;/string></span>
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"set_new_target"</span></span>>Month Target<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set"</span>>Set<span class="tag">&lt;/string></span>
<span class="lineno"> 82 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sign_up"</span>>Sign Up<span class="tag">&lt;/string></span>
<span class="lineno"> 83 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> name</span>=<span class="value">"categories_array"</span>>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:81</span>: <span class="message">"set" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 78 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"target"</span>>Target<span class="tag">&lt;/string></span>
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"set"</span></span>>Set<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 82 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sign_up"</span>>Sign Up<span class="tag">&lt;/string></span>
<span class="lineno"> 83 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> name</span>=<span class="value">"categories_array"</span>>
<span class="lineno"> 84 </span>        <span class="tag">&lt;item></span>Food<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:82</span>: <span class="message">"sign_up" is not translated in "af" (Afrikaans), "en" (English), "zu" (Zulu)</span><br /><pre class="errorlines">
<span class="lineno"> 79 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"balance"</span>>Balance<span class="tag">&lt;/string></span>
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
<span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set"</span>>Set<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 82 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"sign_up"</span></span>>Sign Up<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 83 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> name</span>=<span class="value">"categories_array"</span>>
<span class="lineno"> 84 </span>        <span class="tag">&lt;item></span>Food<span class="tag">&lt;/item></span>
<span class="lineno"> 85 </span>        <span class="tag">&lt;item></span>Transport<span class="tag">&lt;/item></span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationMissingTranslation" style="display: none;">
If an application has more than one locale, then all the strings declared in one language should also be translated in all other languages.<br/>
<br/>
If the string should <b>not</b> be translated, you can add the attribute <code>translatable="false"</code> on the <code>&lt;string></code> element, or you can define all your non-translatable strings in a resource file called <code>donottranslate.xml</code>. Or, you can ignore the issue with a <code>tools:ignore="MissingTranslation"</code> attribute.<br/>
<br/>
You can tell lint (and other tools) which language is the default language in your <code>res/values/</code> folder by specifying <code>tools:locale="languageCode"</code> for the root <code>&lt;resources></code> element in your resource file. (The <code>tools</code> prefix refers to the namespace declaration <code>http://schemas.android.com/tools</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingTranslation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingTranslation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Messages</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingTranslationLink" onclick="reveal('explanationMissingTranslation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingTranslationCardLink" onclick="hideid('MissingTranslationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Security"></a>
<a name="UnsafeIntentLaunch"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnsafeIntentLaunchCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Launched Unsafe Intent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:173</span>: <span class="message">This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.ExpensesActivity. You could either make the component com.example.budgettracker.ExpensesActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer.</span><br /><pre class="errorlines">
<span class="lineno"> 170 </span>      <span class="keyword">val</span> monthYear = date.substring(<span class="number">0</span>, <span class="number">7</span>)
<span class="lineno"> 171 </span>      updateTransactionBalance(db, monthYear, amount)
<span class="lineno"> 172 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@ExpensesActivity</span>, <span class="string">"Data saved successfully"</span>, Toast.LENGTH_SHORT).show()
<span class="caretline"><span class="lineno"> 173 </span>      <span class="keyword">val</span> intent = <span class="warning">getIntent()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 174 </span>      finish()
<span class="lineno"> 175 </span>      startActivity(intent)
<span class="lineno"> 176 </span>  }
</pre>

<ul><span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt">../../src/main/kotlin/com/example/budgettracker/ExpensesActivity.kt</a>:175</span>: <span class="message">The unsafe intent is launched here.</span><br /><pre class="errorlines">
<span class="lineno"> 172 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@ExpensesActivity</span>, <span class="string">"Data saved successfully"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 173 </span>          <span class="keyword">val</span> intent = getIntent()
<span class="lineno"> 174 </span>          finish()
<span class="caretline"><span class="lineno"> 175 </span>          <span class="warning">startActivity(intent)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 176 </span>      }
<span class="lineno"> 177 </span>      db.close()
<span class="lineno"> 178 </span>  }
</pre>
</ul><span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:123</span>: <span class="message">This intent could be coming from an untrusted source. It is later launched by an unprotected component com.example.budgettracker.TransactionsActivity. You could either make the component com.example.budgettracker.TransactionsActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer.</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>          updateTransactionAmounts(db, monthYear, budgetAmount)
<span class="lineno"> 121 </span>      }
<span class="lineno"> 122 </span>      Toast.makeText(<span class="keyword">this</span><span class="annotation">@TransactionsActivity</span>, <span class="string">"Amount Added to your Budget"</span>, Toast.LENGTH_SHORT).show()
<span class="caretline"><span class="lineno"> 123 </span>      <span class="keyword">val</span> intent = <span class="warning">getIntent()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>      finish()
<span class="lineno"> 125 </span>      startActivity(intent)
<span class="lineno"> 126 </span>  }
</pre>

<ul><span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:125</span>: <span class="message">The unsafe intent is launched here.</span><br /><pre class="errorlines">
<span class="lineno"> 122 </span>          Toast.makeText(<span class="keyword">this</span><span class="annotation">@TransactionsActivity</span>, <span class="string">"Amount Added to your Budget"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 123 </span>          <span class="keyword">val</span> intent = getIntent()
<span class="lineno"> 124 </span>          finish()
<span class="caretline"><span class="lineno"> 125 </span>          <span class="warning">startActivity(intent)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 126 </span>      }
<span class="lineno"> 127 </span>      db.close()
<span class="lineno"> 128 </span>  }
</pre>
</ul></div>
<div class="metadata"><div class="explanation" id="explanationUnsafeIntentLaunch" style="display: none;">
Intent that potentially could come from an untrusted source should not be launched from an unprotected component without first being sanitized. See this support FAQ for details: <a href="https://support.google.com/faqs/answer/9267555">https://support.google.com/faqs/answer/9267555</a><br/>To suppress this error, use the issue id "UnsafeIntentLaunch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnsafeIntentLaunch</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnsafeIntentLaunchLink" onclick="reveal('explanationUnsafeIntentLaunch');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnsafeIntentLaunchCardLink" onclick="hideid('UnsafeIntentLaunchCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="ObsoleteLayoutParam"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteLayoutParamCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete layout params</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:64</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_below</code></span><br /><pre class="errorlines">
<span class="lineno">  61 </span><span class="attribute">               </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/username_container"</span>
<span class="lineno">  62 </span>               <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  63 </span>               <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  64 </span>               <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/description"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  65 </span>               <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"24dp"</span>
<span class="lineno">  66 </span>               <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edittext_background"</span>
<span class="lineno">  67 </span>               <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:87</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_below</code></span><br /><pre class="errorlines">
<span class="lineno">  84 </span><span class="attribute">               </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/email_container"</span>
<span class="lineno">  85 </span>               <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  86 </span>               <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  87 </span>               <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/description"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  88 </span>               <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"24dp"</span>
<span class="lineno">  89 </span>               <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno">  90 </span>               <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"16dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteLayoutParam" style="display: none;">
The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteLayoutParam" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteLayoutParam</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteLayoutParamLink" onclick="reveal('explanationObsoleteLayoutParam');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteLayoutParamCardLink" onclick="hideid('ObsoleteLayoutParamCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="VectorPath"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="VectorPathCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Long vector paths</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/eye_close.xml">../../src/main/res/drawable/eye_close.xml</a>:9</span>: <span class="message">Very long vector path (999 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"?attr/colorControlNormal"</span>>
<span class="lineno">  7 </span>  <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"@android:color/white"</span>
<span class="caretline"><span class="lineno">  9 </span>      <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M644,532L586,474Q595,427 559,386Q523,345 466,354L408,296Q425,288 442.5,284Q460,280 480,280Q555,280 607.5,332.5Q660,385 660,460Q660,480 656,497.5Q652,515 644,532ZM772,658L714,602Q752,573 781.5,538.5Q811,504 832,460Q782,359 688.5,299.5Q595,240 480,240Q451,240 423,244Q395,248 368,256L306,194Q347,177 390,168.5Q433,160 480,160Q631,160 749,243.5Q867,327 920,460Q897,519 859.5,569.5Q822,620 772,658ZM792,904L624,738Q589,749 553.5,754.5Q518,760 480,760Q329,760 211,676.5Q93,593 40,460Q61,407 93,361.5Q125,316 166,280L56,168L112,112L848,848L792,904ZM222,336Q193,362 169,393Q145,424 128,460Q178,561 271.5,620.5Q365,680 480,680Q500,680 519,677.5Q538,675 558,672L522,634Q511,637 501,638.5Q491,640 480,640Q405,640 352.5,587.5Q300,535 300,460Q300,449 301.5,439Q303,429 306,418L222,336ZM541,429L541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429Q541,429 541,429ZM390,504Q390,504 390,504Q390,504 390,504L390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Q390,504 390,504Z</span></span><span class="value">"</span>/>
</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/fingerprint.xml">../../src/main/res/drawable/fingerprint.xml</a>:9</span>: <span class="message">Very long vector path (1875 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"?attr/colorControlNormal"</span>>
<span class="lineno">  7 </span>  <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"@android:color/white"</span>
<span class="caretline"><span class="lineno">  9 </span>      <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M481,179Q587,179 681,224.5Q775,270 838,356Q845,365 842.5,372Q840,379 834,384Q828,389 820,388.5Q812,388 806,380Q751,302 664.5,260.5Q578,219 481,219Q384,219 299,260.5Q214,302 158,380Q152,389 144,390Q136,391 130,386Q123,381 121.5,373.5Q120,366 126,358Q188,273 281.5,226Q375,179 481,179ZM481,273Q616,273 713,363Q810,453 810,586Q810,636 774.5,669.5Q739,703 688,703Q637,703 600.5,669.5Q564,636 564,586Q564,553 539.5,530.5Q515,508 481,508Q447,508 422.5,530.5Q398,553 398,586Q398,683 455.5,748Q513,813 604,839Q613,842 616,849Q619,856 617,864Q615,871 609,876Q603,881 594,879Q490,853 424,775.5Q358,698 358,586Q358,536 394,502Q430,468 481,468Q532,468 568,502Q604,536 604,586Q604,619 629,641.5Q654,664 688,664Q722,664 746,641.5Q770,619 770,586Q770,470 685,391Q600,312 482,312Q364,312 279,391Q194,470 194,585Q194,609 198.5,645Q203,681 220,729Q223,738 219.5,745Q216,752 208,755Q200,758 192.5,754.5Q185,751 182,743Q167,704 160.5,665.5Q154,627 154,586Q154,453 250.5,363Q347,273 481,273ZM481,81Q545,81 606,96.5Q667,112 724,141Q733,146 734.5,153Q736,160 733,167Q730,174 723,178Q716,182 706,177Q653,150 596.5,135.5Q540,121 481,121Q423,121 367,134.5Q311,148 260,177Q252,182 244,179.5Q236,177 232,169Q228,161 230,154.5Q232,148 240,143Q296,113 357,97Q418,81 481,81ZM481,370Q574,370 641,432.5Q708,495 708,586Q708,595 702.5,600.5Q697,606 688,606Q680,606 674,600.5Q668,595 668,586Q668,511 612.5,460.5Q557,410 481,410Q405,410 350.5,460.5Q296,511 296,586Q296,667 324,723.5Q352,780 406,837Q412,843 412,851Q412,859 406,865Q400,871 392,871Q384,871 378,865Q319,803 287.5,738.5Q256,674 256,586Q256,495 322,432.5Q388,370 481,370ZM480,566Q489,566 494.5,572Q500,578 500,586Q500,661 554,709Q608,757 680,757Q686,757 697,756Q708,755 720,753Q729,751 735.5,755.5Q742,760 744,769Q746,777 741,783Q736,789 728,791Q710,796 696.5,796.5Q683,797 680,797Q591,797 525.5,737Q460,677 460,586Q460,578 465.5,572Q471,566 480,566Z</span></span><span class="value">"</span>/>
</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/person.xml">../../src/main/res/drawable/person.xml</a>:9</span>: <span class="message">Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"?attr/colorControlNormal"</span>>
<span class="lineno">  7 </span>  <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"@android:color/white"</span>
<span class="caretline"><span class="lineno">  9 </span>      <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M480,480Q414,480 367,433Q320,386 320,320Q320,254 367,207Q414,160 480,160Q546,160 593,207Q640,254 640,320Q640,386 593,433Q546,480 480,480ZM160,800L160,688Q160,654 177.5,625.5Q195,597 224,582Q286,551 350,535.5Q414,520 480,520Q546,520 610,535.5Q674,551 736,582Q765,597 782.5,625.5Q800,654 800,688L800,800L160,800ZM240,720L720,720L720,688Q720,677 714.5,668Q709,659 700,654Q646,627 591,613.5Q536,600 480,600Q424,600 369,613.5Q314,627 260,654Q251,659 245.5,668Q240,677 240,688L240,720ZM480,400Q513,400 536.5,376.5Q560,353 560,320Q560,287 536.5,263.5Q513,240 480,240Q447,240 423.5,263.5Q400,287 400,320Q400,353 423.5,376.5Q447,400 480,400ZM480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320Q480,320 480,320ZM480,720L480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720Q480,720 480,720L480,720Z</span></span><span class="value">"</span>/>
</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/settings_24px.xml">../../src/main/res/drawable/settings_24px.xml</a>:9</span>: <span class="message">Very long vector path (1388 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"?attr/colorControlNormal"</span>>
<span class="lineno">  7 </span>  <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"@android:color/white"</span>
<span class="caretline"><span class="lineno">  9 </span>      <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M370,880L354,752Q341,747 329.5,740Q318,733 307,725L188,775L78,585L181,507Q180,500 180,493.5Q180,487 180,480Q180,473 180,466.5Q180,460 181,453L78,375L188,185L307,235Q318,227 330,220Q342,213 354,208L370,80L590,80L606,208Q619,213 630.5,220Q642,227 653,235L772,185L882,375L779,453Q780,460 780,466.5Q780,473 780,480Q780,487 780,493.5Q780,500 778,507L881,585L771,775L653,725Q642,733 630,740Q618,747 606,752L590,880L370,880ZM440,800L519,800L533,694Q564,686 590.5,670.5Q617,655 639,633L738,674L777,606L691,541Q696,527 698,511.5Q700,496 700,480Q700,464 698,448.5Q696,433 691,419L777,354L738,286L639,328Q617,305 590.5,289.5Q564,274 533,266L520,160L441,160L427,266Q396,274 369.5,289.5Q343,305 321,327L222,286L183,354L269,418Q264,433 262,448Q260,463 260,480Q260,496 262,511Q264,526 269,541L183,606L222,674L321,632Q343,655 369.5,670.5Q396,686 427,694L440,800ZM482,620Q540,620 581,579Q622,538 622,480Q622,422 581,381Q540,340 482,340Q423,340 382.5,381Q342,422 342,480Q342,538 382.5,579Q423,620 482,620ZM480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480Q480,480 480,480L480,480L480,480L480,480Q480,480 480,480Q480,480 480,480L480,480Z</span></span><span class="value">"</span>/>
</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationVectorPath" style="display: none;">
Using long vector paths is bad for performance. There are several ways to make the <code>pathData</code> shorter:<br/>
* Using less precision<br/>
* Removing some minor details<br/>
* Using the Android Studio vector conversion tool<br/>
* Rasterizing the image (converting to PNG)<br/>To suppress this error, use the issue id "VectorPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">VectorPath</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationVectorPathLink" onclick="reveal('explanationVectorPath');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="VectorPathCardLink" onclick="hideid('VectorPathCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InefficientWeight"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InefficientWeightCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Inefficient layout weight</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/settings.xml">../../src/main/res/layout/settings.xml</a>:237</span>: <span class="message">Use a <code>layout_width</code> of <code>0dp</code> instead of <code>185dp</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 234 </span>
<span class="lineno"> 235 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 236 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/title"</span>
<span class="caretline"><span class="lineno"> 237 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"185dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 238 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"64dp"</span>
<span class="lineno"> 239 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"60dp"</span>
<span class="lineno"> 240 </span>        <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInefficientWeight" style="display: none;">
When only a single widget in a <code>LinearLayout</code> defines a weight, it is more efficient to assign a width/height of <code>0dp</code> to it since it will absorb all the remaining space anyway. With a declared width/height of <code>0dp</code> it does not have to measure its own size first.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InefficientWeight" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InefficientWeight</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInefficientWeightLink" onclick="reveal('explanationInefficientWeight');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InefficientWeightCardLink" onclick="hideid('InefficientWeightCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/addexpense.xml">../../src/main/res/layout/addexpense.xml</a>:8</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   5 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   7 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   8 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   9 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/root_layout"</span>
<span class="lineno">  10 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"16dp"</span>>
<span class="lineno">  11 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/root_layout"</span>
<span class="caretline"><span class="lineno">   7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>
<span class="lineno">   9 </span>    <span class="tag">&lt;ImageButton</span><span class="attribute">
</span><span class="lineno">  10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/back_button"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/base.xml">../../src/main/res/layout/base.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/white"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>
<span class="lineno">  9 </span>    <span class="comment">&lt;!-- Footer button 1 --></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;ImageButton</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/dashboard.xml">../../src/main/res/layout/dashboard.xml</a>:8</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   7 </span>
<span class="caretline"><span class="lineno">   8 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   9 </span>
<span class="lineno">  10 </span>    <span class="comment">&lt;!-- Header with search button --></span>
<span class="lineno">  11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/language.xml">../../src/main/res/layout/language.xml</a>:5</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;RelativeLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/white"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  7 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"16dp"</span>>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="OverdrawDivLink" onclick="reveal('OverdrawDiv');" />+ 4 More Occurrences...</button>
<div id="OverdrawDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/login.xml">../../src/main/res/layout/login.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base_Theme_BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/root_layout"</span>
<span class="lineno">   9 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"16dp"</span>>
<span class="lineno">  10 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/notifications.xml">../../src/main/res/layout/notifications.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>
<span class="lineno">   9 </span>    <span class="comment">&lt;!-- Today Section --></span>
<span class="lineno">  10 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>       <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>       <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   6 </span>       <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   7 </span>       <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>       <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/root_layout"</span>
<span class="lineno">   9 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"16dp"</span>
<span class="lineno">  10 </span>       >
</pre>

<span class="location"><a href="../../src/main/res/layout/settings.xml">../../src/main/res/layout/settings.xml</a>:16</span>: <span class="message">Possible overdraw: Root element paints background <code>@android:color/white</code> with a theme that also paints a background (inferred theme is <code>@style/Base.Theme.BudgetTracker</code>)</span><br /><pre class="errorlines">
<span class="lineno">  13 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  14 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  15 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/root_layout"</span>
<span class="caretline"><span class="lineno">  16 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@android:color/white"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  17 </span>
<span class="lineno">  18 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno">  19 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/linearLayout"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:2</span>: <span class="message">The resource <code>R.layout.activity_main</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;androidx.coordinatorlayout.widget.CoordinatorLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span></span>
<span class="lineno">  3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/button_ripple.xml">../../src/main/res/drawable/button_ripple.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.button_ripple</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;ripple</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">android:</span><span class="attribute">color</span>=<span class="value">"?attr/colorControlHighlight"</span>>
<span class="lineno"> 4 </span>    <span class="tag">&lt;item</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/button_background"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/ripple></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:8</span>: <span class="message">The resource <code>R.color.onPrimary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary"</span>>#008cff<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span><span class="comment">&lt;!--    #0275d8--></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"steelblue"</span>>#4682B4<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"onPrimary"</span></span>>#ffffff<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_blue"</span>>#008cff<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_color"</span>>#101518<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"border_color"</span>>#dae1e7<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:11</span>: <span class="message">The resource <code>R.color.border_color</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"onPrimary"</span>>#ffffff<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_blue"</span>>#008cff<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_color"</span>>#101518<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 11 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"border_color"</span></span>>#dae1e7<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"radio_dot_color"</span>>#101518<span class="tag">&lt;/color></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"back_ground"</span>>#b0463a<span class="tag">&lt;/color></span>
<span class="lineno"> 14 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:13</span>: <span class="message">The resource <code>R.color.back_ground</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_color"</span>>#101518<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"border_color"</span>>#dae1e7<span class="tag">&lt;/color></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"radio_dot_color"</span>>#101518<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"back_ground"</span></span>>#b0463a<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span><span class="tag">&lt;/resources></span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 37 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/content_main.xml">../../src/main/res/layout/content_main.xml</a>:2</span>: <span class="message">The resource <code>R.layout.content_main</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;androidx.constraintlayout.widget.ConstraintLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span></span>
<span class="lineno">  3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:2</span>: <span class="message">The resource <code>R.dimen.fab_margin</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno"> 2 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"fab_margin"</span></span>>16dp<span class="tag">&lt;/dimen></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/mipmap-hdpi/ic_launcher.webp">../../src/main/res/mipmap-hdpi/ic_launcher.webp</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-hdpi/ic_launcher.webp" /><span class="message">The resource <code>R.mipmap.ic_launcher</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/ic_launcher_background.xml">../../src/main/res/drawable/ic_launcher_background.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_launcher_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"108dp"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"108dp"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"108"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_launcher_foreground.xml">../../src/main/res/drawable/ic_launcher_foreground.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_launcher_foreground</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">xmlns:</span><span class="attribute">aapt</span>=<span class="value">"http://schemas.android.com/aapt"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"108dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"108dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/mipmap-hdpi/ic_launcher_round.webp">../../src/main/res/mipmap-hdpi/ic_launcher_round.webp</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-hdpi/ic_launcher_round.webp" /><span class="message">The resource <code>R.mipmap.ic_launcher_round</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/mail_24px.xml">../../src/main/res/drawable/mail_24px.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.mail_24px</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"960"</span>
</pre>

<span class="location"><a href="../../src/main/res/menu/menu_main.xml">../../src/main/res/menu/menu_main.xml</a>:1</span>: <span class="message">The resource <code>R.menu.menu_main</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;menu</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  3 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  4 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">"com.example.budgettracker.MainActivity"</span>>
</pre>

<span class="location"><a href="../../src/main/res/drawable/monitoring.xml">../../src/main/res/drawable/monitoring.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.monitoring</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"960"</span>
</pre>

<span class="location"><a href="../../src/main/res/navigation/nav_graph.xml">../../src/main/res/navigation/nav_graph.xml</a>:2</span>: <span class="message">The resource <code>R.navigation.nav_graph</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;navigation</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/nav_graph"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/search.xml">../../src/main/res/drawable/search.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.search</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"960"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/settings_24px.xml">../../src/main/res/drawable/settings_24px.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.settings_24px</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"960"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/spinner_background.xml">../../src/main/res/drawable/spinner_background.xml</a>:17</span>: <span class="message">The resource <code>R.drawable.spinner_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span><span class="comment">     limitations under the License.
</span><span class="lineno"> 15 </span><span class="comment">--></span>
<span class="lineno"> 16 </span>
<span class="caretline"><span class="lineno"> 17 </span><span class="warning"><span class="tag">&lt;selector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;item</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">state_pressed</span>=<span class="value">"true"</span>
<span class="lineno"> 19 </span>          <span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/spinner_press"</span> />
<span class="lineno"> 20 </span>    <span class="tag">&lt;item</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">state_pressed</span>=<span class="value">"false"</span> <span class="prefix">android:</span><span class="attribute">state_focused</span>=<span class="value">"true"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/spinner_item.xml">../../src/main/res/layout/spinner_item.xml</a>:2</span>: <span class="message">The resource <code>R.layout.spinner_item</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 5 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"10dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/spinner_normal.xml">../../src/main/res/drawable/spinner_normal.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.spinner_normal</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;shape</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;solid</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">color</span>=<span class="value">"#FFFFFF"</span>/> <span class="comment">&lt;!-- White color as placeholder --></span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;corners</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">radius</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/shape></span></pre>

<span class="location"><a href="../../src/main/res/drawable/spinner_press.xml">../../src/main/res/drawable/spinner_press.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.spinner_press</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;shape</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;solid</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">color</span>=<span class="value">"#CCCCCC"</span>/> <span class="comment">&lt;!-- Grey color as placeholder --></span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;corners</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">radius</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/shape></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/spinner_select.xml">../../src/main/res/drawable/spinner_select.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.spinner_select</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;shape</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;solid</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">color</span>=<span class="value">"#DDDDDD"</span>/> <span class="comment">&lt;!-- Light grey color as placeholder --></span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;corners</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">radius</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/shape></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:4</span>: <span class="message">The resource <code>R.string.action_settings</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_name"</span>>BudgetTracker<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"action_settings"</span></span>>Settings<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:5</span>: <span class="message">The resource <code>R.string.first_fragment_label</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_name"</span>>BudgetTracker<span class="tag">&lt;/string></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"action_settings"</span>>Settings<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"first_fragment_label"</span></span>>First Fragment<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:6</span>: <span class="message">The resource <code>R.string.second_fragment_label</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_name"</span>>BudgetTracker<span class="tag">&lt;/string></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"action_settings"</span>>Settings<span class="tag">&lt;/string></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"second_fragment_label"</span></span>>Second Fragment<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"search_description"</span>>Search<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:7</span>: <span class="message">The resource <code>R.string.next</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"action_settings"</span>>Settings<span class="tag">&lt;/string></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"next"</span></span>>Next<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"search_description"</span>>Search<span class="tag">&lt;/string></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"chart_description"</span>>Chart<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:8</span>: <span class="message">The resource <code>R.string.previous</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"previous"</span></span>>Previous<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"search_description"</span>>Search<span class="tag">&lt;/string></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"chart_description"</span>>Chart<span class="tag">&lt;/string></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"home_description"</span>>Home<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:9</span>: <span class="message">The resource <code>R.string.search_description</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"search_description"</span></span>>Search<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"chart_description"</span>>Chart<span class="tag">&lt;/string></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"home_description"</span>>Home<span class="tag">&lt;/string></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"expense_description"</span>>Expense<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:10</span>: <span class="message">The resource <code>R.string.chart_description</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"search_description"</span>>Search<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 10 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"chart_description"</span></span>>Chart<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"home_description"</span>>Home<span class="tag">&lt;/string></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"expense_description"</span>>Expense<span class="tag">&lt;/string></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"transaction_description"</span>>Transactions<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:16</span>: <span class="message">The resource <code>R.string.cashmate</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"transaction_description"</span>>Transactions<span class="tag">&lt;/string></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"profile_description"</span>>Profile<span class="tag">&lt;/string></span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add_a_new_expense"</span>>Add a new expense<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 16 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"cashmate"</span></span>>CashMate<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"todo"</span>>TODO<span class="tag">&lt;/string></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"your_personal_budget_tracker"</span>>Your personal budget tracker<span class="tag">&lt;/string></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_email"</span>>Enter email<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:24</span>: <span class="message">The resource <code>R.string.today</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"log_in"</span>>Log in<span class="tag">&lt;/string></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"or_log_in_with"</span>>Or log in with<span class="tag">&lt;/string></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"touch_id"</span>>Touch ID<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"today"</span></span>>Today<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add_a_note"</span>>Add a note<span class="tag">&lt;/string></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"select_category"</span>>Select category<span class="tag">&lt;/string></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0_00"</span>>$0<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:26</span>: <span class="message">The resource <code>R.string.select_category</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 23 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"touch_id"</span>>Touch ID<span class="tag">&lt;/string></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"today"</span>>Today<span class="tag">&lt;/string></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add_a_note"</span>>Add a note<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 26 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"select_category"</span></span>>Select category<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0_00"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"save"</span>>Save<span class="tag">&lt;/string></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"user_name"</span>>Username<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:31</span>: <span class="message">The resource <code>R.string._5_943</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 28 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"save"</span>>Save<span class="tag">&lt;/string></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"user_name"</span>>Username<span class="tag">&lt;/string></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"account_balance"</span>>Account Balance<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 31 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"_5_943"</span></span>>$5,943<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 32 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"last_30_days"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_12"</span>>+12%<span class="tag">&lt;/string></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1d"</span>>1D<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:32</span>: <span class="message">The resource <code>R.string.last_30_days</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"user_name"</span>>Username<span class="tag">&lt;/string></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"account_balance"</span>>Account Balance<span class="tag">&lt;/string></span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_5_943"</span>>$5,943<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 32 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"last_30_days"</span></span>>This month<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_12"</span>>+12%<span class="tag">&lt;/string></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1d"</span>>1D<span class="tag">&lt;/string></span>
<span class="lineno"> 35 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1w"</span>>1W<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:39</span>: <span class="message">The resource <code>R.string.meta</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1m"</span>>1M<span class="tag">&lt;/string></span>
<span class="lineno"> 37 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_3m"</span>>3M<span class="tag">&lt;/string></span>
<span class="lineno"> 38 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1y"</span>>1Y<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 39 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"meta"</span></span>>META<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span><span class="comment">&lt;!--    &lt;string name="budget_info">Your budget for this month is $%1$d&lt;/string>--></span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"meta_platforms"</span>>Meta Platforms<span class="tag">&lt;/string></span>
<span class="lineno"> 42 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"warning_icon"</span>>Warning Icon<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:41</span>: <span class="message">The resource <code>R.string.meta_platforms</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 38 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_1y"</span>>1Y<span class="tag">&lt;/string></span>
<span class="lineno"> 39 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"meta"</span>>META<span class="tag">&lt;/string></span>
<span class="lineno"> 40 </span><span class="comment">&lt;!--    &lt;string name="budget_info">Your budget for this month is $%1$d&lt;/string>--></span>
<span class="caretline"><span class="lineno"> 41 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"meta_platforms"</span></span>>Meta Platforms<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 42 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"warning_icon"</span>>Warning Icon<span class="tag">&lt;/string></span>
<span class="lineno"> 43 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"you_ve_spent_70_of_your_budget_for_the_month"</span>>You’ve spent 70% of your budget for the month<span class="tag">&lt;/string></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"budget_alert"</span>>Budget alert<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:55</span>: <span class="message">The resource <code>R.string.budget_info</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 52 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"heading_list_dash"</span>>Expenditures<span class="tag">&lt;/string></span>
<span class="lineno"> 53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"type"</span>>Type<span class="tag">&lt;/string></span>
<span class="lineno"> 54 </span><span class="comment">&lt;!--    &lt;string name="budget_info">Your budget for this month is $%.2f&lt;/string>--></span>
<span class="caretline"><span class="lineno"> 55 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"budget_info"</span></span>>Your budget for this month is $%d<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 56 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add"</span>>Set<span class="tag">&lt;/string></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"add_money"</span>>Add money<span class="tag">&lt;/string></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"settings"</span>>settings<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:61</span>: <span class="message">The resource <code>R.string.user_profile</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"settings"</span>>settings<span class="tag">&lt;/string></span>
<span class="lineno"> 59 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"language_settings"</span>>Language Settings<span class="tag">&lt;/string></span>
<span class="lineno"> 60 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"clear_user_data"</span>>Clear User Data<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 61 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"user_profile"</span></span>>user profile<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"afrikaans"</span>>Afrikaans<span class="tag">&lt;/string></span>
<span class="lineno"> 63 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"english"</span>>English<span class="tag">&lt;/string></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"language"</span>>Language<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:73</span>: <span class="message">The resource <code>R.string.main_cash</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 70 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"ext_name"</span>>Name<span class="tag">&lt;/string></span>
<span class="lineno"> 71 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"exp_date"</span>>exp_date<span class="tag">&lt;/string></span>
<span class="lineno"> 72 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"date_x"</span>>Date<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 73 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"main_cash"</span></span>>Main_cash<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 74 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"_0"</span>>$0<span class="tag">&lt;/string></span>
<span class="lineno"> 75 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"month"</span>>This month<span class="tag">&lt;/string></span>
<span class="lineno"> 76 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"pick_date"</span>>Pick Date<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:83</span>: <span class="message">The resource <code>R.array.categories_array</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 80 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set_new_target"</span>>Month Target<span class="tag">&lt;/string></span>
<span class="lineno"> 81 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"set"</span>>Set<span class="tag">&lt;/string></span>
<span class="lineno"> 82 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sign_up"</span>>Sign Up<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 83 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"categories_array"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 84 </span>        <span class="tag">&lt;item></span>Food<span class="tag">&lt;/item></span>
<span class="lineno"> 85 </span>        <span class="tag">&lt;item></span>Transport<span class="tag">&lt;/item></span>
<span class="lineno"> 86 </span>        <span class="tag">&lt;item></span>Entertainment<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values/styles.xml">../../src/main/res/values/styles.xml</a>:3</span>: <span class="message">The resource <code>R.style.AppTheme</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;style</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"AppTheme"</span></span> <span class="attribute">parent</span>=<span class="value">"Theme.MaterialComponents.Light.NoActionBar"</span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>        <span class="comment">&lt;!-- Customize your theme here --></span>
<span class="lineno">  5 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>@color/material_dynamic_primary10<span class="tag">&lt;/item></span>
<span class="lineno">  6 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorPrimaryVariant"</span>>@color/material_dynamic_primary20<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/work_24px.xml">../../src/main/res/drawable/work_24px.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.work_24px</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"960"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="IconLauncherShape"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLauncherShapeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">The launcher icon shape should use a distinct silhouette</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/mipmap-hdpi/logos.png">../../src/main/res/mipmap-hdpi/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-hdpi/logos.png" /><span class="message">Launcher icon used as round icon did not have a circular shape</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/mipmap-mdpi/logos.png">../../src/main/res/mipmap-mdpi/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-mdpi/logos.png" /><span class="message">Launcher icon used as round icon did not have a circular shape</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/mipmap-xhdpi/logos.png">../../src/main/res/mipmap-xhdpi/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-xhdpi/logos.png" /><span class="message">Launcher icon used as round icon did not have a circular shape</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/mipmap-xxhdpi/logos.png">../../src/main/res/mipmap-xxhdpi/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-xxhdpi/logos.png" /><span class="message">Launcher icon used as round icon did not have a circular shape</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/mipmap-xxxhdpi/logos.png">../../src/main/res/mipmap-xxxhdpi/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/mipmap-xxxhdpi/logos.png" /><span class="message">Launcher icon used as round icon did not have a circular shape</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLauncherShape" style="display: none;">
According to the Android Design Guide (<a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>) your launcher icons should "use a distinct silhouette", a "three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth."<br/>
<br/>
The unique silhouette implies that your launcher icon should not be a filled square.<br/>To suppress this error, use the issue id "IconLauncherShape" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLauncherShape</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLauncherShapeLink" onclick="reveal('explanationIconLauncherShape');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLauncherShapeCardLink" onclick="hideid('IconLauncherShapeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconLocation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLocationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image defined in density-independent drawable folder</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/logos.png">../../src/main/res/drawable/logos.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/logos.png" /><span class="message">Found bitmap drawable <code>res/drawable/logos.png</code> in densityless folder</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLocation" style="display: none;">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLocation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLocationLink" onclick="reveal('explanationIconLocation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLocationCardLink" onclick="hideid('IconLocationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="TextFields"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TextFieldsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing inputType</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/addexpense.xml">../../src/main/res/layout/addexpense.xml</a>:53</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno">  50 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  51 </span>        <span class="prefix">app:</span><span class="attribute">tint</span>=<span class="value">"@color/black"</span> />
<span class="lineno">  52 </span>
<span class="caretline"><span class="lineno">  53 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  54 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText2"</span>
<span class="lineno">  55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"336dp"</span>
<span class="lineno">  56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"135dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addexpense.xml">../../src/main/res/layout/addexpense.xml</a>:66</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno">  63 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span>
<span class="lineno">  64 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
<span class="lineno">  65 </span>
<span class="caretline"><span class="lineno">  66 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  67 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno">  68 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"174dp"</span>
<span class="lineno">  69 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"81dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:107</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno"> 104 </span>        <span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/linearLayout5"</span>
<span class="lineno"> 105 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"-3dp"</span> />
<span class="lineno"> 106 </span>
<span class="caretline"><span class="lineno"> 107 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 108 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText5"</span>
<span class="lineno"> 109 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"152dp"</span>
<span class="lineno"> 110 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:121</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/black"</span>
<span class="lineno"> 119 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"13sp"</span> />
<span class="lineno"> 120 </span>
<span class="caretline"><span class="lineno"> 121 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 122 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno"> 123 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"167dp"</span>
<span class="lineno"> 124 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"49dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/login.xml">../../src/main/res/layout/login.xml</a>:48</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno">  45 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edittext_background"</span>
<span class="lineno">  46 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno">  47 </span>
<span class="caretline"><span class="lineno">  48 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  49 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/email_input"</span>
<span class="lineno">  50 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  51 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:71</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno">  68 </span>               <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"16dp"</span>
<span class="lineno">  69 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno">  70 </span>
<span class="caretline"><span class="lineno">  71 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  72 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/username_input"</span>
<span class="lineno">  73 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  74 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:94</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno">  91 </span>               <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edittext_background"</span>
<span class="lineno">  92 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno">  93 </span>
<span class="caretline"><span class="lineno">  94 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  95 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/email_input"</span>
<span class="lineno">  96 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  97 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/settings.xml">../../src/main/res/layout/settings.xml</a>:186</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"8dp"</span>
<span class="lineno"> 184 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 187 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno"> 188 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 189 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationTextFields" style="display: none;">
Providing an <code>inputType</code> attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). <br/>
<br/>
The lint detector also looks at the <code>id</code> of the view, and if the id offers a hint of the purpose of the field (for example, the <code>id</code> contains the phrase <code>phone</code> or <code>email</code>), then lint will also ensure that the <code>inputType</code> contains the corresponding type attributes.<br/>
<br/>
If you really want to keep the text field generic, you can suppress this warning by setting <code>inputType="text"</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TextFields" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">TextFields</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTextFieldsLink" onclick="reveal('explanationTextFields');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TextFieldsCardLink" onclick="hideid('TextFieldsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Autofill"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutofillCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use Autofill</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/addexpense.xml">../../src/main/res/layout/addexpense.xml</a>:53</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  50 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  51 </span>        <span class="prefix">app:</span><span class="attribute">tint</span>=<span class="value">"@color/black"</span> />
<span class="lineno">  52 </span>
<span class="caretline"><span class="lineno">  53 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  54 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText2"</span>
<span class="lineno">  55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"336dp"</span>
<span class="lineno">  56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"135dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addexpense.xml">../../src/main/res/layout/addexpense.xml</a>:66</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  63 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span>
<span class="lineno">  64 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
<span class="lineno">  65 </span>
<span class="caretline"><span class="lineno">  66 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  67 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno">  68 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"174dp"</span>
<span class="lineno">  69 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"81dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:107</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 104 </span>        <span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/linearLayout5"</span>
<span class="lineno"> 105 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"-3dp"</span> />
<span class="lineno"> 106 </span>
<span class="caretline"><span class="lineno"> 107 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 108 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText5"</span>
<span class="lineno"> 109 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"152dp"</span>
<span class="lineno"> 110 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:121</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/black"</span>
<span class="lineno"> 119 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"13sp"</span> />
<span class="lineno"> 120 </span>
<span class="caretline"><span class="lineno"> 121 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 122 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno"> 123 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"167dp"</span>
<span class="lineno"> 124 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"49dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:71</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  68 </span>               <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"16dp"</span>
<span class="lineno">  69 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno">  70 </span>
<span class="caretline"><span class="lineno">  71 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  72 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/username_input"</span>
<span class="lineno">  73 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  74 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="AutofillDivLink" onclick="reveal('AutofillDiv');" />+ 4 More Occurrences...</button>
<div id="AutofillDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:94</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  91 </span>               <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edittext_background"</span>
<span class="lineno">  92 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno">  93 </span>
<span class="caretline"><span class="lineno">  94 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  95 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/email_input"</span>
<span class="lineno">  96 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  97 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:117</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 114 </span>               <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"16dp"</span>
<span class="lineno"> 115 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>>
<span class="lineno"> 116 </span>
<span class="caretline"><span class="lineno"> 117 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 118 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/password_input"</span>
<span class="lineno"> 119 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 120 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:150</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 147 </span>               <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>
<span class="lineno"> 148 </span>               <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"ObsoleteLayoutParam"</span>>
<span class="lineno"> 149 </span>
<span class="caretline"><span class="lineno"> 150 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 151 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/password_input_confrim"</span>
<span class="lineno"> 152 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 153 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/settings.xml">../../src/main/res/layout/settings.xml</a>:186</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"8dp"</span>
<span class="lineno"> 184 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 185 </span>
<span class="caretline"><span class="lineno"> 186 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 187 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/editText4"</span>
<span class="lineno"> 188 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 189 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationAutofill" style="display: none;">
Specify an <code>autofillHints</code> attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.<br/>
<br/>
The hints can have any value, but it is recommended to use predefined values like 'username' for a username or 'creditCardNumber' for a credit card number. For a list of all predefined autofill hint constants, see the <code>AUTOFILL_HINT_</code> constants in the <code>View</code> reference at <a href="https://developer.android.com/reference/android/view/View.html">https://developer.android.com/reference/android/view/View.html</a>.<br/>
<br/>
You can mark a view unimportant for autofill by specifying an <code>importantForAutofill</code> attribute on that view or a parent view. See <a href="https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)">https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)</a>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/autofill.html">https://developer.android.com/guide/topics/text/autofill.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Autofill" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Autofill</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutofillLink" onclick="reveal('explanationAutofill');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutofillCardLink" onclick="hideid('AutofillCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:56</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>    androidTestImplementation(libs.ext.junit)
<span class="lineno"> 54 </span>    androidTestImplementation(libs.espresso.core)
<span class="lineno"> 55 </span>    implementation(libs.biometric)
<span class="caretline"><span class="lineno"> 56 </span>    implementation(<span class="warning">"com.squareup.retrofit2:retrofit:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:57</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 54 </span>    androidTestImplementation(libs.espresso.core)
<span class="lineno"> 55 </span>    implementation(libs.biometric)
<span class="lineno"> 56 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="caretline"><span class="lineno"> 57 </span>    implementation(<span class="warning">"com.squareup.retrofit2:converter-gson:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 60 </span>    implementation("com.google.firebase:firebase-bom:33.1.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:58</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 55 </span>    implementation(libs.biometric)
<span class="lineno"> 56 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="caretline"><span class="lineno"> 58 </span>    implementation(<span class="warning">"com.jjoe64:graphview:4.2.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 60 </span>    implementation("com.google.firebase:firebase-bom:33.1.2")
<span class="lineno"> 61 </span>    implementation ("androidx.work:work-runtime:2.8.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:59</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="caretline"><span class="lineno"> 59 </span>    implementation (<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>    implementation("com.google.firebase:firebase-bom:33.1.2")
<span class="lineno"> 61 </span>    implementation ("androidx.work:work-runtime:2.8.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:60</span>: <span class="message">Use version catalog instead (com.google.firebase:firebase-bom is already available as <code>firebase-bom</code>, but using version 32.1.2 instead)</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="caretline"><span class="lineno"> 60 </span>    implementation(<span class="warning">"com.google.firebase:firebase-bom:33.1.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 61 </span>    implementation ("androidx.work:work-runtime:2.8.0")
<span class="lineno"> 62 </span>
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:61</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>    implementation("com.jjoe64:graphview:4.2.2")
<span class="lineno"> 59 </span>    implementation ("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 60 </span>    implementation("com.google.firebase:firebase-bom:33.1.2")
<span class="caretline"><span class="lineno"> 61 </span>    implementation (<span class="warning">"androidx.work:work-runtime:2.8.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>
<span class="lineno"> 63 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/list_bug.xml">../../src/main/res/layout/list_bug.xml</a>:8</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>>
<span class="lineno">  7 </span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  9 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/list_icon"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/list_item.xml">../../src/main/res/layout/list_item.xml</a>:7</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/list_icon"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:130</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 127 </span>                   <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 128 </span>                   <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"HardcodedText"</span> />
<span class="lineno"> 129 </span>
<span class="caretline"><span class="lineno"> 130 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 131 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/password_eye"</span>
<span class="lineno"> 132 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 133 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:163</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 160 </span>                   <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 161 </span>                   <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"HardcodedText"</span> />
<span class="lineno"> 162 </span>
<span class="caretline"><span class="lineno"> 163 </span>               <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 164 </span><span class="attribute">                   </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/password_eye_confrim"</span>
<span class="lineno"> 165 </span>                   <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 166 </span>                   <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:143</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 140 </span>      targetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">1</span>))
<span class="lineno"> 141 </span>      budgetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">2</span>))
<span class="lineno"> 142 </span>  } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 143 </span>      balanceTextView.text = <span class="warning"><span class="string">"0.00"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 144 </span>      targetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 145 </span>      budgetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 146 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:143</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 140 </span>      targetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">1</span>))
<span class="lineno"> 141 </span>      budgetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">2</span>))
<span class="lineno"> 142 </span>  } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 143 </span>      balanceTextView.text = <span class="string">"</span><span class="warning"><span class="string">0.00</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 144 </span>      targetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 145 </span>      budgetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 146 </span>  }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:144</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span>          budgetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">2</span>))
<span class="lineno"> 142 </span>      } <span class="keyword">else</span> {
<span class="lineno"> 143 </span>          balanceTextView.text = <span class="string">"0.00"</span>
<span class="caretline"><span class="lineno"> 144 </span>          targetTextView.text = <span class="warning"><span class="string">"0.00"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 145 </span>          budgetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 146 </span>      }
<span class="lineno"> 147 </span>  } catch (e: ParseException) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:144</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span>          budgetTextView.text = String.format(Locale.getDefault(), <span class="string">"%.2f"</span>, cursor.getDouble(<span class="number">2</span>))
<span class="lineno"> 142 </span>      } <span class="keyword">else</span> {
<span class="lineno"> 143 </span>          balanceTextView.text = <span class="string">"0.00"</span>
<span class="caretline"><span class="lineno"> 144 </span>          targetTextView.text = <span class="string">"</span><span class="warning"><span class="string">0.00</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 145 </span>          budgetTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 146 </span>      }
<span class="lineno"> 147 </span>  } catch (e: ParseException) {
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:145</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 142 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 143 </span>                balanceTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 144 </span>                targetTextView.text = <span class="string">"0.00"</span>
<span class="caretline"><span class="lineno"> 145 </span>                budgetTextView.text = <span class="warning"><span class="string">"0.00"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 146 </span>            }
<span class="lineno"> 147 </span>        } catch (e: ParseException) {
<span class="lineno"> 148 </span>            Log.w(<span class="string">"DatabaseHelper"</span>, <span class="string">"Error parsing date: ${</span>e.message<span class="string">}"</span>)
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="SetTextI18nDivLink" onclick="reveal('SetTextI18nDiv');" />+ 7 More Occurrences...</button>
<div id="SetTextI18nDiv" style="display: none">
<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt">../../src/main/kotlin/com/example/budgettracker/SettingsActivity.kt</a>:145</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 142 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 143 </span>                balanceTextView.text = <span class="string">"0.00"</span>
<span class="lineno"> 144 </span>                targetTextView.text = <span class="string">"0.00"</span>
<span class="caretline"><span class="lineno"> 145 </span>                budgetTextView.text = <span class="string">"</span><span class="warning"><span class="string">0.00</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 146 </span>            }
<span class="lineno"> 147 </span>        } catch (e: ParseException) {
<span class="lineno"> 148 </span>            Log.w(<span class="string">"DatabaseHelper"</span>, <span class="string">"Error parsing date: ${</span>e.message<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:333</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 330 </span>                }
<span class="lineno"> 331 </span>            }
<span class="lineno"> 332 </span>            
<span class="caretline"><span class="lineno"> 333 </span>            budgetInfoTextView.text = <span class="warning"><span class="string">"Total Budget for this month: R$totalBudget"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 334 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 335 </span>            budgetInfoTextView.text = <span class="string">"Total Budget for this month: R0"</span>
<span class="lineno"> 336 </span>        }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:333</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 330 </span>                }
<span class="lineno"> 331 </span>            }
<span class="lineno"> 332 </span>            
<span class="caretline"><span class="lineno"> 333 </span>            budgetInfoTextView.text = <span class="string">"</span><span class="warning"><span class="string">Total Budget for this month: R</span></span><span class="string">$totalBudget"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 334 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 335 </span>            budgetInfoTextView.text = <span class="string">"Total Budget for this month: R0"</span>
<span class="lineno"> 336 </span>        }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:335</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 332 </span>                
<span class="lineno"> 333 </span>                budgetInfoTextView.text = <span class="string">"Total Budget for this month: R$totalBudget"</span>
<span class="lineno"> 334 </span>            } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 335 </span>                budgetInfoTextView.text = <span class="warning"><span class="string">"Total Budget for this month: R0"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 336 </span>            }
<span class="lineno"> 337 </span>        }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:335</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 332 </span>                
<span class="lineno"> 333 </span>                budgetInfoTextView.text = <span class="string">"Total Budget for this month: R$totalBudget"</span>
<span class="lineno"> 334 </span>            } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 335 </span>                budgetInfoTextView.text = <span class="string">"</span><span class="warning"><span class="string">Total Budget for this month: R0</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 336 </span>            }
<span class="lineno"> 337 </span>        }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:340</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 337 </span>            }
<span class="lineno"> 338 </span>
<span class="lineno"> 339 </span>            override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {
<span class="caretline"><span class="lineno"> 340 </span>                budgetInfoTextView.text = <span class="warning"><span class="string">"Total Budget for this month: R0"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 341 </span>            }
<span class="lineno"> 342 </span>        })
<span class="lineno"> 343 </span>    }
</pre>

<span class="location"><a href="../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt">../../src/main/kotlin/com/example/budgettracker/TransactionsActivity.kt</a>:340</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 337 </span>            }
<span class="lineno"> 338 </span>
<span class="lineno"> 339 </span>            override <span class="keyword">fun</span> onFailure(<span class="annotation">@NonNull</span> call: Call&lt;List&lt;Record>>, <span class="annotation">@NonNull</span> t: Throwable) {
<span class="caretline"><span class="lineno"> 340 </span>                budgetInfoTextView.text = <span class="string">"</span><span class="warning"><span class="string">Total Budget for this month: R0</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 341 </span>            }
<span class="lineno"> 342 </span>        })
<span class="lineno"> 343 </span>    }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/addtransaction.xml">../../src/main/res/layout/addtransaction.xml</a>:58</span>: <span class="message">Hardcoded string "Your budget for this month is $0", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  56 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  57 </span>            <span class="prefix">android:</span><span class="attribute">paddingTop</span>=<span class="value">"4dp"</span>
<span class="caretline"><span class="lineno">  58 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Your budget for this month is $0"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  59 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#637488"</span>
<span class="lineno">  60 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span> />
<span class="lineno">  61 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/register.xml">../../src/main/res/layout/register.xml</a>:42</span>: <span class="message">Hardcoded string "Create Account", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/back_button"</span>
<span class="lineno">  40 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"70dp"</span>
<span class="lineno">  41 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"-40dp"</span>
<span class="caretline"><span class="lineno">  42 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Create Account"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  43 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_color"</span>
<span class="lineno">  44 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"28sp"</span>
<span class="lineno">  45 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RelativeOverlap"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RelativeOverlapCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overlapping items in RelativeLayout</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/notifications.xml">../../src/main/res/layout/notifications.xml</a>:49</span>: <span class="message"><code>@id/button</code> can overlap <code>@id/back_button</code> if @id/button grows due to localized text expansion</span><br /><pre class="errorlines">
<span class="lineno">  46 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"25sp"</span>
<span class="lineno">  47 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
<span class="lineno">  48 </span>
<span class="caretline"><span class="lineno">  49 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  50 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/button"</span>
<span class="lineno">  51 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  52 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRelativeOverlap" style="display: none;">
If relative layout has text or button items aligned to left and right sides they can overlap each other due to localized text expansion unless they have mutual constraints like <code>toEndOf</code>/<code>toStartOf</code>.<br/>To suppress this error, use the issue id "RelativeOverlap" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RelativeOverlap</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRelativeOverlapLink" onclick="reveal('explanationRelativeOverlap');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RelativeOverlapCardLink" onclick="hideid('RelativeOverlapCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">BadConfigurationProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
An <code>android.app.Application</code> must implement <code>androidx.work.Configuration.Provider</code><br/>
for on-demand initialization.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadPeriodicWorkRequestEnqueue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>enqueue()</code> for `PeriodicWorkRequest`s, you might end up enqueuing<br/>
duplicate requests unintentionally. You should be using<br/>
<code>enqueueUniquePeriodicWork</code> with an <code>ExistingPeriodicWorkPolicy.KEEP</code> instead.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IdleBatteryChargingConstraints<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Some devices are never considered charging and idle at the same time.<br/>
Consider removing one of these constraints.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPeriodicWorkRequestInterval<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The interval duration for a <code>PeriodicWorkRequest</code> must be at least 15 minutes.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RemoveWorkManagerInitializer<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If an <code>android.app.Application</code> implements <code>androidx.work.Configuration.Provider</code>,<br/>
the default <code>androidx.startup.InitializationProvider</code> needs to be removed from the<br/>
AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyForegroundServiceType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using the setForegroundAsync() API, the application must override &lt;service /> entry for <code>SystemForegroundService</code> to include the foreground service type in the  <code>AndroidManifest.xml</code> file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyJobSchedulerIdRange<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>JobScheduler</code> APIs directly, <code>WorkManager</code> requires that developers specify a range of <code>JobScheduler</code> ids that are safe for <code>WorkManager</code> to use so the `id`s do not collide. <br/>
For more information look at <code>androidx.work.Configuration.Builder.setJobSchedulerJobIdRange(int, int)</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRxSetProgress2<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>setCompletableProgress(...)</code> instead of `setProgress(...) in <code>RxWorker</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorkerHasAPublicModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When you define a ListenableWorker which is constructed using the <br/>
default WorkerFactory, the ListenableWorker sub-type needs to be public.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>isEquivalentTo(PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>